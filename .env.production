# Production Environment Configuration
# NODE_ENV is automatically set by deployment platforms
NEXT_PUBLIC_APP_ENV=production

# Authentication Backend URL
NEXT_PUBLIC_GRAPHQL_URL=https://ng-auth.ngnair.com/graphql
AUTH_URL=https://ng-auth.ngnair.com/api/auth

# Cookie Configuration for Iframe Support
# Set this to your domain for cross-subdomain cookie sharing
# Example: .yourdomain.com (note the leading dot)
NEXT_PUBLIC_COOKIE_DOMAIN=.ngnair.com

# Trusted Domain Patterns for Iframe Communication
# Comma-separated list of domain patterns that are allowed to embed this auth system
# Supports wildcards: ng-*.com matches ng-ob.com, ng-support.com, etc.
NEXT_PUBLIC_TRUSTED_DOMAINS=ng-*.ngnair.com,*.ngnair.com,ngnair.com

# Redirect URLs for microservices integration
# Fallback URL when no redirect parameter is provided
NEXT_PUBLIC_FALLBACK_URL=https://ng-accounts.ngnair.com

# Auth service URL (this service)
NEXT_PUBLIC_AUTH_SERVICE_URL=https://ng-auth.ngnair.com

# Other configuration
NEXT_PUBLIC_APP_URL=https://ng-auth.ngnair.com
