import { graphql } from '../generated';

export const Login = graphql(`
  mutation Authclient_login($email: String!, $browserId: String, $password: String!) {
    authclient_login(email: $email, browserId: $browserId, password: $password) {
      ... on ClientItemAuthenticationWithPasswordSuccess {
        sessionToken
      }
      ... on ClientItemAuthenticationWithPasswordFailure {
        message
      }
    }
  }
`);

export const Register = graphql(`
  mutation Authclient_register(
    $email: String!
    $password: String!
    $phoneNumber: String
    $otpSid: String
    $browserId: String
    $firstName: String
    $lastName: String
    $affiliateId: String
    $title: String
  ) {
    authclient_register(
      email: $email
      password: $password
      phoneNumber: $phoneNumber
      otpSid: $otpSid
      browserId: $browserId
      firstName: $firstName
      lastName: $lastName
      affiliateId: $affiliateId
      title: $title
    )
  }
`);

export const SendEmailVerification = graphql(`
  mutation SendEmailVerification(
    $email: String!
    $firstName: String!
    $lastName: String!
    $title: String
  ) {
    sendEmailVerification(
      email: $email
      firstName: $firstName
      lastName: $lastName
      title: $title
    ) {
      success
      message
    }
  }
`);

export const VerifyEmailAndSetPassword = graphql(`
  mutation VerifyEmailAndSetPassword(
    $token: String!
    $password: String!
    $phoneNumber: String!
    $otpSid: String!
  ) {
    verifyEmailAndSetPassword(
      token: $token
      password: $password
      phoneNumber: $phoneNumber
      otpSid: $otpSid
    ) {
      success
      sessionToken
      message
    }
  }
`);
