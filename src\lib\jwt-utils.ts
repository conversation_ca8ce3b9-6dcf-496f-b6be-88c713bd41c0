// JWT utilities for microservice integration
import { authService, JWTPayload } from './api/auth-service';

export interface JWKSKey {
  kty: string;
  use: string;
  kid: string;
  n: string;
  e: string;
}

export interface JWKS {
  keys: JWKSKey[];
}

/**
 * Utility class for JWT validation and management in microservices
 */
export class JWTUtils {
  private static jwksCache: JWKS | null = null;
  private static jwksCacheExpiry: number = 0;
  private static readonly CACHE_DURATION = 3600000; // 1 hour in milliseconds

  /**
   * Get JWKS from the auth service with caching
   */
  static async getJWKS(): Promise<JWKS> {
    const now = Date.now();
    
    // Return cached JWKS if still valid
    if (this.jwksCache && now < this.jwksCacheExpiry) {
      return this.jwksCache;
    }

    try {
      const jwks = await authService.getJWKS();
      this.jwksCache = jwks;
      this.jwksCacheExpiry = now + this.CACHE_DURATION;
      return jwks;
    } catch (error) {
      console.error('Failed to fetch JWKS:', error);
      throw error;
    }
  }

  /**
   * Decode JWT payload without verification (client-side only)
   */
  static decodePayload(token?: string): JWTPayload | null {
    if (token) {
      try {
        const parts = token.split('.');
        if (parts.length !== 3) return null;
        return JSON.parse(atob(parts[1])) as JWTPayload;
      } catch {
        return null;
      }
    }
    return authService.decodeTokenPayload();
  }

  /**
   * Check if token is expired
   */
  static isTokenExpired(token?: string): boolean {
    const payload = this.decodePayload(token);
    if (!payload || !payload.exp) return true;

    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  }

  /**
   * Validate token locally (no API calls)
   */
  static isTokenValid(token?: string): boolean {
    if (token) {
      const payload = this.decodePayload(token);
      return payload !== null && !this.isTokenExpired(token);
    }
    return authService.validateToken();
  }

  /**
   * Get user context from current or provided token
   */
  static getUserContext(token?: string) {
    if (token) {
      const payload = this.decodePayload(token);
      if (!payload) {
        return {
          userId: null,
          accounts: [],
          partners: [],
          selectedAccountId: null,
          selectedPartnerId: null,
        };
      }

      return {
        userId: payload.sub || null,
        accounts: payload.accounts || [],
        partners: payload.partners || [],
        selectedAccountId: payload.selected_account_id || null,
        selectedPartnerId: payload.selected_partner_id || null,
      };
    }
    
    return authService.getCurrentUserContext();
  }

  /**
   * Check if user has specific scope for an account
   */
  static hasAccountScope(accountId: string, scope: string, token?: string): boolean {
    const context = this.getUserContext(token);
    const account = context.accounts.find(acc => acc.id === accountId);
    return account ? account.scopes.includes(scope) : false;
  }

  /**
   * Check if user has specific scope for a partner
   */
  static hasPartnerScope(partnerId: string, scope: string, token?: string): boolean {
    const context = this.getUserContext(token);
    const partner = context.partners.find(p => p.id === partnerId);
    return partner ? partner.scopes.includes(scope) : false;
  }

  /**
   * Check if user has any of the specified scopes for the selected account
   */
  static hasAnyAccountScope(scopes: string[], token?: string): boolean {
    const context = this.getUserContext(token);
    if (!context.selectedAccountId) return false;
    
    const account = context.accounts.find(acc => acc.id === context.selectedAccountId);
    if (!account) return false;
    
    return scopes.some(scope => account.scopes.includes(scope));
  }

  /**
   * Check if user has any of the specified scopes for the selected partner
   */
  static hasAnyPartnerScope(scopes: string[], token?: string): boolean {
    const context = this.getUserContext(token);
    if (!context.selectedPartnerId) return false;
    
    const partner = context.partners.find(p => p.id === context.selectedPartnerId);
    if (!partner) return false;
    
    return scopes.some(scope => partner.scopes.includes(scope));
  }

  /**
   * Get all available scopes for the current user across all accounts and partners
   */
  static getAllUserScopes(token?: string): string[] {
    const context = this.getUserContext(token);
    const allScopes = new Set<string>();
    
    context.accounts.forEach(account => {
      account.scopes.forEach(scope => allScopes.add(scope));
    });
    
    context.partners.forEach(partner => {
      partner.scopes.forEach(scope => allScopes.add(scope));
    });
    
    return Array.from(allScopes);
  }

  /**
   * Check if user is in a specific account context
   */
  static isInAccountContext(accountId: string, token?: string): boolean {
    const context = this.getUserContext(token);
    return context.selectedAccountId === accountId;
  }

  /**
   * Check if user is in a specific partner context
   */
  static isInPartnerContext(partnerId: string, token?: string): boolean {
    const context = this.getUserContext(token);
    return context.selectedPartnerId === partnerId;
  }

  /**
   * Generate auth redirect URL for microservices
   */
  static generateAuthRedirectUrl(returnUrl?: string): string {
    const authServiceUrl = process.env.NEXT_PUBLIC_AUTH_SERVICE_URL || 'https://ng-auth-dev.dev1.ngnair.com';
    const redirectUrl = returnUrl || window.location.href;
    const encodedRedirect = encodeURIComponent(redirectUrl);
    return `${authServiceUrl}/login?redirect=${encodedRedirect}`;
  }

  /**
   * Redirect to auth service for login
   */
  static redirectToAuth(returnUrl?: string): void {
    const authUrl = this.generateAuthRedirectUrl(returnUrl);
    window.location.href = authUrl;
  }

  /**
   * Clear all authentication data
   */
  static clearAuth(): void {
    // Clear cookies
    document.cookie = 'health-token=; domain=.ngnair.com; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    document.cookie = 'health-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    
    // Clear localStorage
    localStorage.removeItem('health-token');
    
    // Clear sessionStorage
    sessionStorage.removeItem('device_id');
  }
}

// Export convenience functions
export const {
  decodePayload,
  isTokenExpired,
  getUserContext,
  hasAccountScope,
  hasPartnerScope,
  hasAnyAccountScope,
  hasAnyPartnerScope,
  getAllUserScopes,
  isInAccountContext,
  isInPartnerContext,
  generateAuthRedirectUrl,
  redirectToAuth,
  clearAuth,
} = JWTUtils;
