#!/usr/bin/env node

/**
 * Environment Switcher Script
 * 
 * Usage:
 *   node scripts/switch-env.js development
 *   node scripts/switch-env.js production
 * 
 * This script switches between development and production environments
 * by updating the NODE_ENV variable in the .env file.
 */

const fs = require('fs');
const path = require('path');

const envFile = path.join(__dirname, '..', '.env');
const args = process.argv.slice(2);

if (args.length === 0) {
  console.log('Usage: node scripts/switch-env.js <environment>');
  console.log('');
  console.log('Available environments:');
  console.log('  development  - Shows debug info, development footer, console logs');
  console.log('  production   - Clean UI, no debug info, production-ready');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/switch-env.js development');
  console.log('  node scripts/switch-env.js production');
  process.exit(1);
}

const environment = args[0].toLowerCase();

if (!['development', 'production'].includes(environment)) {
  console.error('❌ Invalid environment. Use "development" or "production"');
  process.exit(1);
}

try {
  // Read current .env file
  let envContent = fs.readFileSync(envFile, 'utf8');
  
  // Update NEXT_PUBLIC_APP_ENV
  const appEnvRegex = /^NEXT_PUBLIC_APP_ENV=.*$/m;
  const newAppEnv = `NEXT_PUBLIC_APP_ENV=${environment}`;

  if (appEnvRegex.test(envContent)) {
    envContent = envContent.replace(appEnvRegex, newAppEnv);
  } else {
    envContent = `${newAppEnv}\n${envContent}`;
  }
  
  // Write updated content
  fs.writeFileSync(envFile, envContent);
  
  console.log(`✅ Environment switched to: ${environment}`);
  console.log('');
  
  if (environment === 'development') {
    console.log('🔧 Development mode enabled:');
    console.log('  - Development footer with debug info');
    console.log('  - Console logging for debugging');
    console.log('  - Token and OTP display for testing');
    console.log('  - Debug UI components visible');
  } else {
    console.log('🚀 Production mode enabled:');
    console.log('  - Clean user interface');
    console.log('  - No debug information displayed');
    console.log('  - No console logging');
    console.log('  - Professional appearance');
  }
  
  console.log('');
  console.log('💡 Run "npm run build" to apply changes');
  
} catch (error) {
  console.error('❌ Error updating .env file:', error.message);
  process.exit(1);
}
