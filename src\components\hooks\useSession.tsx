import { useEffect, useState } from 'react';
import Cookies from 'js-cookie';

const generateRandomString = (length: number) => {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

export const useBrowserSessionID = () => {
  const [sessionID, setSessionID] = useState<string>('');

  useEffect(() => {
    // Try cookie first, fallback to localStorage
    let session = Cookies.get('ngn-sessionID');

    if (!session) {
      // Check localStorage for backward compatibility
      session = localStorage.getItem('ngn-sessionID') || '';
    }

    if (session) {
      setSessionID(session);
      // Ensure it's also stored in cookie
      Cookies.set('ngn-sessionID', session, {
        expires: 30, // 30 days
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'none',
        domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN || undefined,
      });
    } else {
      const newSession = generateRandomString(20);
      // Store in both cookie and localStorage
      Cookies.set('ngn-sessionID', newSession, {
        expires: 30, // 30 days
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'none',
        domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN || undefined,
      });
      localStorage.setItem('ngn-sessionID', newSession);
      setSessionID(newSession);
    }
  }, []);

  return {
    id: sessionID,
    regenerate: () => {
      const newSession = generateRandomString(20);
      // Store in both cookie and localStorage
      Cookies.set('ngn-sessionID', newSession, {
        expires: 30, // 30 days
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'none',
        domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN || undefined,
      });
      localStorage.setItem('ngn-sessionID', newSession);
      setSessionID(newSession);
    },
  };
};
