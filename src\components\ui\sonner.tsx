"use client"

import { Toaster as Sonner, ToasterProps } from "sonner"
import { useTheme } from "../ThemeProvider"

const Toaster = ({ ...props }: ToasterProps) => {
  const { actualTheme } = useTheme()

  const toasterStyle: React.CSSProperties = actualTheme === 'dark' ? {
    ['--normal-bg' as any]: "#1f2937",
    ['--normal-text' as any]: "#f9fafb",
    ['--normal-border' as any]: "#374151",
  } : {
    ['--normal-bg' as any]: "white",
    ['--normal-text' as any]: "black",
    ['--normal-border' as any]: "#e5e7eb",
  }

  return (
    <Sonner
      theme={actualTheme}
      className="toaster group"
      style={toasterStyle}
      {...props}
    />
  )
}

export { Toaster }
