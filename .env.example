# Authentication Backend URL
NEXT_PUBLIC_GRAPHQL_URL=http://localhost:3001/graphql
AUTH_URL=http://localhost:3001/api/auth

# Cookie Configuration for Iframe Support
# Set this to your domain for cross-subdomain cookie sharing
# Example: .yourdomain.com (note the leading dot)
NEXT_PUBLIC_COOKIE_DOMAIN=

# Trusted Domain Patterns for Iframe Communication
# Comma-separated list of domain patterns that are allowed to embed this auth system
# Supports wildcards: ng-*.com matches ng-ob.com, ng-support.com, etc.
# Real examples from your infrastructure:
# - ng-support-fe-dev.dev1.ngnair.com (support frontend dev environment)
# - ng-ob.ng-dev.ngnair.com (onboarding dev environment)
NEXT_PUBLIC_TRUSTED_DOMAINS=ng-*-fe-*.dev*.ngnair.com,ng-*.ng-*.ngnair.com,ng-*.dev*.ngnair.com,ng-*-fe-*.ngnair.com,*.ngnair.com,ngnair.com,localhost,127.0.0.1

# App Environment (development or production)
# Controls development features visibility
# In development: Shows debug info, development footer, console logs
# In production: Hides all development features for clean user experience
# NOTE: NODE_ENV is automatically set by deployment platforms
NEXT_PUBLIC_APP_ENV=development

# Redirect URLs for microservices integration
# Fallback URL when no redirect parameter is provided
NEXT_PUBLIC_FALLBACK_URL=https://ng-accounts-fe-dev.dev1.ngnair.com

# Auth service URL (this service)
NEXT_PUBLIC_AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com

# Other configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
