# Test Account Creation Flow - Complete Guide

## 🧪 Testing the Complete Registration Flow

### Prerequisites
1. Ensure backend server is running
2. Open browser with Developer Tools (F12)
3. Clear browser cache and cookies
4. Enable console logging

### Test Scenario 1: Registration with Redirect URL

#### Step 1: Start Registration with Redirect
```
URL: http://localhost:3000/register?redirect=https://ng-account-fe-dev.dev1.ngnair.com/dashboard
```

#### Step 2: Fill Registration Form
- **Email**: <EMAIL>
- **First Name**: Test
- **Last Name**: User
- **Phone**: +**********

#### Step 3: Verify Email
- Check email for verification link
- Click verification link
- Should redirect to: `/verify-email?token=...&user_id=...&redirect=...`

#### Step 4: Set Password
- Should redirect to: `/set-password?user_id=...&redirect=...`
- Enter strong password (score ≥ 2)
- Confirm password

#### Expected Console Output
```
=== SET PASSWORD DEBUG INFO ===
User ID: [uuid]
Password length: [length]
Password strength score: [2-4]
API Base URL: https://ng-auth-dev.dev1.ngnair.com/api/v1
Request payload: { user_id: "[uuid]", password: "[REDACTED]" }
Current URL: /set-password?user_id=...&redirect=...
Device ID in localStorage: [device_id or null]
Device ID in sessionStorage: [device_id or null]
================================

=== SET PASSWORD API CALL ===
Endpoint: /set_password
Method: POST
User ID: [uuid]
Password length: [length]
Full URL: https://ng-auth-dev.dev1.ngnair.com/api/v1/set_password
============================
```

#### Expected Success Flow
1. **API Call Success**: Status 200, receives access_token and device_id
2. **Toast Message**: "Account created successfully! You are now logged in."
3. **Redirect**: After 1.5 seconds, redirects to original URL

### Test Scenario 2: Registration without Redirect URL

#### Step 1: Start Registration
```
URL: http://localhost:3000/register
```

#### Step 2-4: Same as above

#### Expected Success Flow
1. **API Call Success**: Status 200
2. **Toast Message**: Success message
3. **Redirect**: Redirects to fallback URL (ng-account-fe-dev.dev1.ngnair.com)

## 🔍 Debugging HTTP 500 Errors

### Check Network Tab
1. **Request Details**:
   - URL: `https://ng-auth-dev.dev1.ngnair.com/api/v1/set_password`
   - Method: POST
   - Headers: Content-Type: application/json
   - Body: `{"user_id":"...","password":"..."}`

2. **Response Details**:
   - Status: 500 Internal Server Error
   - Response body: Check for error message

### Check Console Errors
Look for these error patterns:
```
=== ERROR DETAILS ===
Error object: [error details]
Status code: 500
Error message: [backend error message]
Full error: [JSON error object]
====================

=== API ERROR DETAILS ===
URL: https://ng-auth-dev.dev1.ngnair.com/api/v1/set_password
Status: 500
Status Text: Internal Server Error
Error Data: [backend response]
Request Headers: [request headers]
========================

HTTP 500 Error Details:
- Check backend server logs
- Verify API endpoint: /api/v1/set_password
- Confirm user_id exists in database
- Check password validation on backend
- Verify database connectivity
```

## 🔧 Common Issues and Solutions

### Issue 1: User ID Not Found
**Symptoms**: HTTP 500 with user not found error
**Solution**: 
- Verify user completed email verification
- Check user exists in database
- Ensure user_id is valid UUID

### Issue 2: Password Validation Failure
**Symptoms**: HTTP 422 or 500 with password validation error
**Solution**:
- Check backend password requirements
- Ensure frontend and backend use same zxcvbn version
- Verify password meets all criteria

### Issue 3: Database Connection Issues
**Symptoms**: HTTP 500 with database error
**Solution**:
- Check database server status
- Verify connection credentials
- Check connection pool settings

### Issue 4: Missing Device ID
**Symptoms**: HTTP 500 with device ID error
**Solution**:
- Check if device_id is required by backend
- Verify device_id is being sent in headers
- Check localStorage/sessionStorage for device_id

## 🧪 Manual API Testing

### Test with curl
```bash
# Replace with actual user_id from registration
curl -X POST https://ng-auth-dev.dev1.ngnair.com/api/v1/set_password \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "X-Device-ID: your-device-id" \
  -d '{
    "user_id": "your-user-id-here",
    "password": "TestPassword123!"
  }' \
  -v
```

### Expected Success Response
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "device_id": "device-uuid-here"
}
```

### Expected Error Response (500)
```json
{
  "message": "Internal server error",
  "error": "Specific error details"
}
```

## 🔄 Testing Redirect Functionality

### Test Redirect URL Preservation
1. **Start with redirect**: `/register?redirect=https://example.com`
2. **Check verify-email**: URL should contain `&redirect=https://example.com`
3. **Check set-password**: URL should contain `&redirect=https://example.com`
4. **After success**: Should redirect to `https://example.com`

### Test Redirect Validation
Try these URLs to test validation:
- ✅ Valid: `https://ng-account-fe-dev.dev1.ngnair.com/dashboard`
- ✅ Valid: `https://ng-support.com/help`
- ❌ Invalid: `https://malicious-site.com`
- ❌ Invalid: `javascript:alert('xss')`

### Check Console for Redirect Debug
```
🔄 Redirect Handler: {
  redirectUrl: "https://ng-account-fe-dev.dev1.ngnair.com/dashboard",
  redirectSource: "url-parameter",
  isValid: true,
  fallbackUrl: "https://ng-account-fe-dev.dev1.ngnair.com"
}
```

## 📋 Testing Checklist

### Before Testing
- [ ] Backend server is running
- [ ] Database is accessible
- [ ] Email service is configured
- [ ] Frontend is in development mode for debugging

### During Testing
- [ ] Monitor browser console for debug messages
- [ ] Check Network tab for API calls
- [ ] Verify redirect URLs are preserved
- [ ] Test with various password strengths

### After Success
- [ ] Verify user can log in with new credentials
- [ ] Check that access token is properly stored
- [ ] Confirm redirect works to intended destination
- [ ] Test logout and re-login flow

### If Errors Occur
- [ ] Collect all console error messages
- [ ] Note exact steps to reproduce
- [ ] Check backend server logs
- [ ] Verify database state
- [ ] Test API endpoint directly with curl

## 🚨 Escalation

If issues persist after following this guide:
1. Collect all debugging information
2. Check backend server logs for the exact timestamp
3. Verify database connectivity and user state
4. Test API endpoints directly
5. Contact backend team with specific error details
