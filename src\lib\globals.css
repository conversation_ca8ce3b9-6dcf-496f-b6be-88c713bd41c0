@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');
@import "tailwindcss";

@theme {
  --color-primary-300: #8c9eb4;
  --color-primary-500: #3c5b80;
  --color-primary-600: #2d4a6b;
  --color-primary-700: #1e3456;
  --color-primary-800: #0f1e41;
  --color-muted-foreground: #6b7280;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
}

body {
  margin: 0;
  line-height: normal;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;

    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;

    --primary: #3c5b80;
    --primary-shade: #8c9eb4;
    --primary-foreground: #fff;

    --secondary: #ed6b4c;
    --secondary-foreground: #f4a795;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 71.4% 4.1%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;

    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;

    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;

    --primary: #8c9eb4;
    --primary-shade: #3c5b80;
    --primary-foreground: #1e3456;

    --secondary: #f4a795;
    --secondary-foreground: #ed6b4c;

    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;

    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;

    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;
  }
}

@keyframes spinner {
  from {
    stroke-dashoffset: calc(3.14159265358979 * 50 * 2);
    transform: rotateZ(0deg);
  }
  to {
    stroke-dashoffset: calc(calc(3.14159265358979 * 50 * 2) * -1);
    transform: rotateZ(720deg);
  }
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 11px;
  height: 8px;
  background-color: #e2e2e2;
}

::-webkit-scrollbar-track {
  background: #f8f8f8;
}

::-webkit-scrollbar-thumb {
  border-radius: 15px;
  background: #d3d3d3;
}

::-webkit-scrollbar-thumb:hover {
  cursor: pointer;
  background: #969696;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar {
  background-color: #374151;
}

.dark ::-webkit-scrollbar-track {
  background: #1f2937;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

@layer utilities {
  .scrollbar-medium::-webkit-scrollbar {
    width: 5px !important;
  }
}

/* HTML: <div class="loader"></div> */
.square-loader {
  display: inline-flex;
  gap: 5px;
}
.square-loader:before,
.square-loader:after {
  content: '';
  width: 25px;
  aspect-ratio: 1;
  box-shadow: 0 0 0 3px inset #000;
  animation: l4 1.5s infinite;
}
.square-loader:after {
  --s: -1;
  animation-delay: 0.75s;
}
@keyframes l4 {
  0% {
    transform: scaleX(var(--s, 1)) translate(0) rotate(0);
  }
  16.67% {
    transform: scaleX(var(--s, 1)) translate(-50%) rotate(0);
  }
  33.33% {
    transform: scaleX(var(--s, 1)) translate(-50%) rotate(90deg);
  }
  50%,
  100% {
    transform: scaleX(var(--s, 1)) translate(0) rotate(90deg);
  }
}

.react-datepicker-wrapper {
  width: 100%;
}

.hide-scrollbar-thumb::-webkit-scrollbar {
  background-color: transparent;
  width: 0px;
  scrollbar-width: 0px;
  background-color: transparent;
  border-radius: 0px;
}

.noScrollbar::-webkit-scrollbar {
  display: none;
}

.noScrollbar::-webkit-scrollbar-thumb {
  display: none;
}

.noScrollbar::-webkit-scrollbar-track {
  display: none;
}

.noScrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* React Phone Input Customization */
.react-tel-input {
  font-family: inherit;
}

.react-tel-input .form-control {
  width: 100% !important;
  height: auto !important;
  padding: 10px 12px !important;
  padding-left: 60px !important; /* Increased padding to prevent flag overlap */
  font-size: 14px !important;
  border-radius: 8px !important;
  border: 1px solid #d1d5db !important;
  background-color: #f9fafb !important;
  color: #111827 !important;
}

.react-tel-input .form-control:focus {
  border-color: #2563eb !important;
  box-shadow: 0 0 0 1px #2563eb !important;
  outline: none !important;
}

.react-tel-input .flag-dropdown {
  border-radius: 8px 0 0 8px !important;
  border: 1px solid #d1d5db !important;
  background-color: #f9fafb !important;
}

.react-tel-input .flag-dropdown:hover {
  background-color: #f3f4f6 !important;
}

.react-tel-input .selected-flag {
  padding: 0 8px !important;
}

.react-tel-input .country-list {
  border-radius: 8px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #d1d5db !important;
  background-color: #ffffff !important;
}

/* Dark mode React Phone Input */
.dark .react-tel-input .form-control {
  border: 1px solid #4b5563 !important;
  background-color: #374151 !important;
  color: #f9fafb !important;
}

.dark .react-tel-input .form-control:focus {
  border-color: #8c9eb4 !important;
  box-shadow: 0 0 0 1px #8c9eb4 !important;
}

.dark .react-tel-input .flag-dropdown {
  border: 1px solid #4b5563 !important;
  background-color: #374151 !important;
}

.dark .react-tel-input .flag-dropdown:hover {
  background-color: #4b5563 !important;
}

.dark .react-tel-input .country-list {
  border: 1px solid #4b5563 !important;
  background-color: #1f2937 !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3) !important;
}

.dark .react-tel-input .country-list .country {
  background-color: #1f2937 !important;
  color: #f9fafb !important;
}

.dark .react-tel-input .country-list .country:hover {
  background-color: #374151 !important;
}

.dark .react-tel-input .country-list .country.highlight {
  background-color: #4b5563 !important;
}
