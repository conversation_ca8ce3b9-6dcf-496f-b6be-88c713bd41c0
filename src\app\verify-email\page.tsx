/* eslint-disable @next/next/no-img-element */
'use client';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useEffect, useState } from 'react';
import { authService, ApiError } from '@/lib/api/auth-service';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/bootstrap.css';
import Image from 'next/image';

// EmailVerificationStep: Renders the UI for entering and verifying the email token sent to the user.
const EmailVerificationStep = ({
  userInfo,
  onVerify,
  error,
  submitting,
  tokenInput,
  setTokenInput
}: {
  userInfo: any;
  onVerify: () => void;
  error: string;
  submitting: boolean;
  tokenInput: string;
  setTokenInput: (token: string) => void;
}) => {
  return (
    <>
      <CardHeader className="space-y-1">
        <div className="text-center">
          <img src="/images/logo.png" className="mx-auto h-16" alt="NGnair Logo" />
        </div>
        <div className="text-center">
          <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
            Verify Your Email
          </CardTitle>
          <p className="text-sm text-gray-600">
            Enter the verification token from your email to continue registration.
          </p>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="token">Verification Token</Label>
          <Input
            id="token"
            placeholder="Enter verification token"
            type="text"
            value={tokenInput}
            onChange={(e) => setTokenInput(e.target.value)}
            className="font-mono"
          />
        </div>

        {/* Development Info - Only show in development */}
        {process.env.NEXT_PUBLIC_APP_ENV === 'development' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-800 mb-2">Development Info:</h4>
            <div className="space-y-2 text-xs text-blue-700">
              <div>
                <span className="font-medium">User ID:</span>
                <div className="font-mono bg-blue-100 p-1 rounded mt-1 break-all">
                  {userInfo?.userId}
                </div>
              </div>
              <div>
                <span className="font-medium">Token (copy this):</span>
                <div className="font-mono bg-blue-100 p-1 rounded mt-1 break-all cursor-pointer"
                     onClick={() => {
                       navigator.clipboard.writeText(userInfo?.token || '');
                       setTokenInput(userInfo?.token || '');
                     }}>
                  {userInfo?.token}
                </div>
                <p className="text-xs text-blue-600 mt-1">Click to copy and auto-fill</p>
              </div>
            </div>
          </div>
        )}

        <Button
          className="w-full bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
          onClick={onVerify}
          disabled={submitting || !tokenInput.trim()}
        >
          {submitting ? 'Verifying Email...' : 'Verify Email'}
        </Button>

        {error && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}
      </CardContent>
    </>
  );
};

interface PhoneVerificationProps {
  form: {
    phone: string;
  };
  setForm: (form: any) => void;
  onContinue: () => void;
  error: string;
  submitting: boolean;
}

// PhoneVerificationStep: Renders the UI for entering and submitting the user's phone number for verification.
const PhoneVerificationStep = (props: PhoneVerificationProps) => {
  return (
    <>
      <CardHeader className="space-y-1">
        <div className="text-center">
          <Image
            src="/images/logo.png"
            alt="Company Logo"
            width={330}
            height={81}
            priority
            quality={100}
            className="mx-auto"
          />
        </div>
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Verify Your Phone Number
          </h2>
          <p className="text-sm text-gray-600">
            Please enter your phone number to receive a verification code
          </p>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <div className="relative w-full">
            <PhoneInput
              value={props.form.phone}
              onChange={(phone) => {
                props.setForm({ ...props.form, phone });
              }}
              placeholder="****** 567 8900"
              countryCodeEditable={true}
              autoFormat={true}
              country={'us'}
              preferredCountries={['us', 'ca']}
              containerStyle={{
                width: '100%',
              }}
              inputStyle={{
                width: '100%',
                height: '42px',
                fontSize: '14px',
                paddingLeft: '60px', // Increased from 48px to 60px to prevent flag overlap
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                backgroundColor: 'white',
                boxSizing: 'border-box',
              }}
              buttonStyle={{
                width: '60px', // matches input paddingLeft
                borderRadius: '8px 0 0 8px',
                border: '1px solid #d1d5db',
                backgroundColor: 'white',
                height: '42px',
              }}
              dropdownStyle={{
                borderRadius: '8px',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                border: '1px solid #d1d5db',
              }}
            />
          </div>
        </div>
        <Button
          className="w-full bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
          onClick={props.onContinue}
          disabled={props.submitting}
        >
          {props.submitting ? 'Sending Code...' : 'Send Verification Code'}
        </Button>
        {props.error && (
          <p className="mx-auto -mt-2 text-center text-xs text-red-600">{props.error}</p>
        )}
      </CardContent>
    </>
  );
};

interface OTPVerificationProps {
  onBack: () => void;
  error: string;
  onResend: () => void;
  onVerify: (code: string) => void;
  submitting: boolean;
  pendingToResend: number;
}

// OTPVerificationStep: Renders the UI for entering the OTP code sent to the user's phone and handles OTP input logic.
const OTPVerificationStep = (props: OTPVerificationProps) => {
  const [code, setCode] = useState(['', '', '', '', '', '']);

  // Handle pasting a 6-digit code into any input
  const handleCodePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    const paste = e.clipboardData.getData('text');
    if (/^\d{6}$/.test(paste)) {
      setCode(paste.split(''));
      // Optionally, focus the last input
      const lastInput = document.getElementById('code-5');
      lastInput?.focus();
      e.preventDefault();
    }
  };

  const handleCodeChange = (index: number, value: string) => {
    // Only allow digits
    if (!/^\d?$/.test(value)) return;
    const newCode = [...code];
    newCode[index] = value;
    setCode(newCode);

    // Auto-focus next input
    if (value && index < 5) {
      const nextInput = document.getElementById(`code-${index + 1}`);
      nextInput?.focus();
    }
  };

  const handleCodeKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace') {
      if (code[index] === '') {
        if (index > 0) {
          const prevInput = document.getElementById(`code-${index - 1}`);
          prevInput?.focus();
          const newCode = [...code];
          newCode[index - 1] = '';
          setCode(newCode);
        }
      } else {
        const newCode = [...code];
        newCode[index] = '';
        setCode(newCode);
      }
      // Prevent default to avoid browser navigation
      e.preventDefault();
    }
    // Allow left/right arrow navigation
    if (e.key === 'ArrowLeft' && index > 0) {
      const prevInput = document.getElementById(`code-${index - 1}`);
      prevInput?.focus();
      e.preventDefault();
    }
    if (e.key === 'ArrowRight' && index < 5) {
      const nextInput = document.getElementById(`code-${index + 1}`);
      nextInput?.focus();
      e.preventDefault();
    }
  };

  return (
    <>
      <CardHeader className="space-y-1">
        <div className="text-center">
          <img src="/images/logo.png" className="mx-auto h-16" alt="NGnair Logo" />
        </div>
        <div className="text-left">
          <h2 className="text-2xl font-bold text-gray-900 mb-2 text-center">
            Enter Verification Code
          </h2>
          <p className="text-sm text-gray-600">
            We&apos;ve sent a 6-digit code to your phone number. Please enter it below.
          </p>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="code-0" className="sr-only">
            Verification Code
          </Label>
          <div className="mx-auto flex max-w-xs justify-between">
            {code.map((digit, index) => (
              <Input
                key={index}
                id={`code-${index}`}
                type="text"
                inputMode="numeric"
                pattern="\d*"
                maxLength={1}
                className="h-12 w-12 text-center text-2xl"
                value={digit}
                onChange={(e) => handleCodeChange(index, e.target.value)}
                onKeyDown={(e) => handleCodeKeyDown(index, e)}
                onPaste={handleCodePaste}
                autoComplete="one-time-code"
              />
            ))}
          </div>
        </div>
        <Button
          className="w-full bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
          onClick={() => props.onVerify(code.join(''))}
          disabled={props.submitting || code.join('').length !== 6}
        >
          {props.submitting ? 'Verifying...' : 'Verify Code'}
        </Button>
        {props.error && (
          <p className="mx-auto -mt-2 text-center text-xs text-red-600">{props.error}</p>
        )}
        <div className="flex flex-col space-y-2 items-center">
          {!props.pendingToResend ? (
            <Button 
              variant="link" 
              onClick={props.onResend} 
              className="text-sm text-blue-600 hover:text-blue-500 cursor-pointer"
              disabled={props.submitting}
            >
              Resend code
            </Button>
          ) : (
            <p className="text-center text-sm text-gray-600">
              Resend code in {props.pendingToResend} seconds
            </p>
          )}
          <Button 
            variant="link" 
            onClick={props.onBack} 
            className="text-sm text-gray-600 hover:text-gray-500 cursor-pointer"
            disabled={props.submitting}
          >
            Change phone number
          </Button>
        </div>
      </CardContent>
    </>
  );
};

// VerifyEmailPage: Main component that manages the multi-step email and phone verification process for user registration.
export default function VerifyEmailPage() {
  const [step, setStep] = useState<'email' | 'phone' | 'otp'>('email');
  const [form, setForm] = useState({
    phone: '+1',
  });
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [pendingToResend, setPendingToResend] = useState(0);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [otpCode, setOtpCode] = useState<string>('');
  const [tokenInput, setTokenInput] = useState<string>('');

  useEffect(() => {
    // Get user info from URL params or sessionStorage
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const userId = urlParams.get('user_id');

    // Try to get from sessionStorage if not in URL
    const storedData = sessionStorage.getItem('registrationData');

    if (token && userId) {
      // Set user info and show manual verification step
      setUserInfo({ userId, token });
      setStep('email');
    } else if (storedData) {
      const data = JSON.parse(storedData);
      setUserInfo(data);
      setStep('email');
    } else {
      // Redirect to register if no valid token
      window.location.href = '/register';
    }
  }, []);

  const handleManualEmailVerification = async () => {
    if (!userInfo?.userId) {
      setError('User information not found. Please start registration again.');
      return;
    }

    if (!tokenInput.trim()) {
      setError('Please enter the verification token.');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      await authService.verifyEmail({
        user_id: userInfo.userId,
        token: tokenInput.trim()
      });

      // Email verified successfully, move to phone verification
      setStep('phone');
      setSubmitting(false);
    } catch (err) {
      console.error('Email verification error:', err);
      const apiError = err as ApiError;
      setError(apiError.message || 'Email verification failed. Please check your token and try again.');
      setSubmitting(false);
    }
  };

  useEffect(() => {
    setError('');
  }, [form]);

  useEffect(() => {
    if (pendingToResend > 0) {
      const timer = setTimeout(() => {
        setPendingToResend(pendingToResend - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [pendingToResend]);

  const handleSendOTP = async () => {
    if (!form.phone || form.phone.length < 4) {
      setError('Please enter a valid phone number');
      return;
    }

    if (!userInfo?.userId) {
      setError('User information not found. Please start registration again.');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      const response = await authService.sendOtp({
        user_id: userInfo.userId,
        phone: form.phone
      });

      // Store the OTP for development display (only in development)
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development' && response.otp) {
        setOtpCode(response.otp);
      }

      setStep('otp');
      setPendingToResend(60);
    } catch (err) {
      console.error('Send OTP error:', err);
      const apiError = err as ApiError;
      setError(apiError.message || 'Failed to send verification code. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleVerifyOTP = async (code: string) => {
    if (code.length !== 6) {
      setError('Please enter a 6-digit code');
      return;
    }

    if (!userInfo?.userId) {
      setError('User information not found. Please start registration again.');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      const otpResponse = await authService.verifyOtp({
        user_id: userInfo.userId,
        otp: code
      });

      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('=== OTP VERIFICATION SUCCESS ===');
        console.log('OTP Response:', otpResponse);
        console.log('Phone verification completed for user:', userInfo.userId);
        console.log('Phone number:', form.phone);
        console.log('================================');
      }

      // Store phone number and any authentication tokens for password setup
      const updatedUserInfo = {
        ...userInfo,
        phone: form.phone,
        // Store any authentication tokens from OTP verification
        otpVerificationToken: otpResponse.access_token || otpResponse.session_token || otpResponse.verification_token,
        phoneVerified: true,
        otpVerifiedAt: new Date().toISOString()
      };
      sessionStorage.setItem('registrationData', JSON.stringify(updatedUserInfo));

      // Store any access token if provided
      if (otpResponse.access_token) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Storing access token from OTP verification:', otpResponse.access_token);
        }
        // Store in auth storage for subsequent API calls
        sessionStorage.setItem('otp_access_token', otpResponse.access_token);
      }

      // Preserve redirect URL when going to password setup
      const urlParams = new URLSearchParams(window.location.search);
      const redirectParam = urlParams.get('redirect');
      const setPasswordUrl = redirectParam
        ? `/set-password?user_id=${userInfo.userId}&redirect=${encodeURIComponent(redirectParam)}`
        : `/set-password?user_id=${userInfo.userId}`;

      window.location.href = setPasswordUrl;
    } catch (err) {
      console.error('Verify OTP error:', err);
      const apiError = err as ApiError;
      setError(apiError.message || 'Invalid verification code. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleResendOTP = async () => {
    if (!userInfo?.userId) {
      setError('User information not found. Please start registration again.');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      const response = await authService.sendOtp({
        user_id: userInfo.userId,
        phone: form.phone
      });

      // Update the OTP for development display (only in development)
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development' && response.otp) {
        setOtpCode(response.otp);
      }

      setPendingToResend(60);
    } catch (err) {
      console.error('Resend OTP error:', err);
      const apiError = err as ApiError;
      setError(apiError.message || 'Failed to resend code. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (!userInfo) {
    return (
      <div className="from-primary/20 to-secondary/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
        <Card className="w-full max-w-md border shadow-xl">
          <CardContent className="p-6">
            <p className="text-center">Loading...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="from-primary/20 to-secondary/20 flex min-h-screen flex-col items-center justify-center bg-gradient-to-br p-4">
      <Card className="w-full max-w-md border shadow-xl">
        {step === 'email' ? (
          <EmailVerificationStep
            userInfo={userInfo}
            onVerify={handleManualEmailVerification}
            error={error}
            submitting={submitting}
            tokenInput={tokenInput}
            setTokenInput={setTokenInput}
          />
        ) : step === 'phone' ? (
          <PhoneVerificationStep
            form={form}
            setForm={setForm}
            onContinue={handleSendOTP}
            error={error}
            submitting={submitting}
          />
        ) : (
          <OTPVerificationStep
            onBack={() => {
              setStep('phone');
              setOtpCode(''); // Clear OTP when going back
            }}
            error={error}
            onResend={handleResendOTP}
            onVerify={handleVerifyOTP}
            submitting={submitting}
            pendingToResend={pendingToResend}
          />
        )}
      </Card>

      {/* Development Footer - Only show in development */}
      {process.env.NEXT_PUBLIC_APP_ENV === 'development' && userInfo && (
        <div className="mt-4 w-full max-w-md">
          <div className="bg-gray-800 text-white text-xs p-3 rounded-lg">
            <div className="text-center text-gray-300 mb-2 font-medium">🔧 Development Mode</div>
            <div className="space-y-1">
              <div>
                <span className="text-gray-400">User ID:</span>
                <div className="font-mono text-green-400 break-all">{userInfo.userId}</div>
              </div>
              <div>
                <span className="text-gray-400">Token:</span>
                <div className="font-mono text-blue-400 break-all">{userInfo.token}</div>
              </div>
              {userInfo.email && (
                <div>
                  <span className="text-gray-400">Email:</span>
                  <span className="text-yellow-400 ml-1">{userInfo.email}</span>
                </div>
              )}
              {form.phone && (
                <div>
                  <span className="text-gray-400">Phone:</span>
                  <span className="text-purple-400 ml-1">{form.phone}</span>
                </div>
              )}
              {otpCode && (
                <div>
                  <span className="text-gray-400">OTP Code:</span>
                  <div className="font-mono text-red-400 text-lg font-bold bg-gray-700 p-2 rounded mt-1">
                    {otpCode}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
