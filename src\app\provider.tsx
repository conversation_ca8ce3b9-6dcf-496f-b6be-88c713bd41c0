// src/app/provider.tsx
"use client";

import { ApolloProvider } from "@apollo/client";
import { apolloClient } from "../lib/graphql/ApolloClient";
import { IframeAuthHandler } from "../components/IframeAuthHandler";
import { ThemeProvider } from "../components/ThemeProvider";
import { Toaster } from "@/components/ui/sonner";
import React from "react";

export function ClientProviders({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider defaultTheme="system" storageKey="ngnair-theme">
      <ApolloProvider client={apolloClient}>
        <IframeAuthHandler>
          {children}
        </IframeAuthHandler>
        <Toaster />
      </ApolloProvider>
    </ThemeProvider>
  );
}
