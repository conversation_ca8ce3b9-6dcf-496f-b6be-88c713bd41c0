import React from 'react';
import Image from 'next/image';
import { Card, CardTitle, CardContent } from './ui/card';

const Section: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div className="mb-8">
    <h2 className="mb-4 text-xl font-semibold text-blue-600">{title}</h2>
    <div className="space-y-4">{children}</div>
  </div>
);

export const TermsOfServiceModal: React.FC<{ open: boolean; onClose: () => void }> = ({ open, onClose }) => {
  // Accessibility: Escape key closes modal, scroll lock
  React.useEffect(() => {
    if (!open) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };
    document.addEventListener('keydown', handleKeyDown);
    // scroll lock
    const originalOverflow = document.body.style.overflow;
    document.body.style.overflow = 'hidden';
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = originalOverflow;
    };
  }, [open, onClose]);
  if (!open) return null;
  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
      role="dialog"
      aria-modal="true"
      tabIndex={-1}
      onClick={e => {
        //close effect
        if (e.target === e.currentTarget) onClose();
      }}
    >
      <Card className="w-full max-w-lg md:max-w-2xl lg:max-w-3xl p-2 sm:p-6 relative">
        <CardContent className="max-h-[85vh] overflow-y-auto relative">
          <div className="flex flex-col items-center mb-2">
            <Image
              src="/images/logo.png"
              alt="Company Logo"
              width={330}
              height={81}
              priority
              quality={100}
              style={{ objectFit: 'contain' }}
            />
          </div>
          <CardTitle className="text-center mb-5 font-bold">NGnair Payments - Terms of Service</CardTitle>
          <button
            onClick={onClose}
            className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-xl font-bold cursor-pointer"
            aria-label="Close"
          >
            ×
          </button>
            <Section title="1. Introduction and Acceptance">
              <p style={{ textIndent: '2em', textAlign: 'justify' }}>
                This Terms of Service Agreement (&quot;Agreement&quot;) is entered into between NGnair Payments
                (&quot;Provider,&quot; &quot;we,&quot; &quot;us,&quot; or &quot;our&quot;) and you (&quot;Merchant&quot; or &quot;you&quot;), the user of our
                payment processing software. By accessing or using our software, you agree to be bound
                by these terms and conditions. If you do not agree, you may not use the service.
              </p>
            </Section>
          <Section title="2. Service Provided">
            <p style={{ textIndent: '2em', textAlign: 'justify' }}>
              The Provider provides payment processing software solutions for merchants. The service
              is limited to the payment processing software provided and does not include any
              additional services or products unless explicitly agreed upon in writing. We reserve the
              right to modify or discontinue the service at any time, with notice to our merchants.
            </p>
          </Section>
          <Section title="3. User Obligations">
            <ul className="list-disc space-y-2 pl-6">
              <li>
                The Merchant is responsible for maintaining their own hardware and internet connection
                to access the service.
              </li>
              <li>
                You are required to comply with all applicable laws and regulations while using our
                service.
              </li>
              <li>
                The Merchant is solely responsible for the accuracy and legality of the data entered
                into our system.
              </li>
            </ul>
          </Section>
          <Section title="4. Service Availability and Downtime">
            <p style={{ textIndent: '2em', textAlign: 'justify' }}>
              The Provider will make commercially reasonable efforts to maintain service availability.
              However, we cannot guarantee 100% uptime, and we will not be liable for any damages,
              including loss of business or revenue, resulting from downtime or service outages.
              Scheduled maintenance may occur, and merchants will be notified in advance. Unplanned
              outages due to technical issues or force majeure events will be handled as quickly as
              possible, but no guarantees are made regarding recovery time.
            </p>
          </Section>
          <Section title="5. Limited Use License and Intellectual Property Protection">
            <p style={{ textIndent: '2em', textAlign: 'justify' }}>
              The Provider grants the Merchant a limited, non-exclusive, non-transferable, and
              revocable license to use the NGnair Payments software solely for the purpose of handling
              payment processing. This license is granted for lawful use only, specifically for the
              Merchant&apos;s internal business operations related to payment transactions.
            </p>
            <p className="mt-4">The Merchant is prohibited from:</p>
            <ul className="list-disc space-y-2 pl-6">
              <li>Copying, modifying, distributing, or reverse engineering the software.</li>
              <li>
                Creating derivative works based on the software or attempting to replicate its
                functionality.
              </li>
              <li>
                Utilizing the software&apos;s underlying business processes, technology, or confidential
                information for any purpose other than its intended use.
              </li>
            </ul>
            <p className="mt-4" style={{ textIndent: '2em', textAlign: 'justify' }}>
              Any unauthorized use, reproduction, or distribution of the Provider&apos;s software or
              intellectual property may result in immediate termination of this Agreement, legal
              action, and damages to the fullest extent permissible under the law.
            </p>
          </Section>
          <Section title="6. Use of Contact Information">
            <p style={{ textIndent: '2em', textAlign: 'justify' }}>
              By using the Provider&apos;s services, the Merchant agrees that the Provider may use the
              contact information provided during account registration to communicate regarding the
              Merchant&apos;s account, service updates, and other administrative purposes. Additionally,
              the Provider may send marketing communications related to new products, services, and
              promotional offers.
            </p>
            <p className="mt-4" style={{ textIndent: '2em', textAlign: 'justify' }}>
              Your use of the Provider&apos;s services is subject to the Provider&apos;s Privacy Policy. Please
              review our Privacy Policy, which also governs the site and informs users of our data
              collection practices.
            </p>
            <h4 className="mb-2 mt-4 text-blue-600">SMS Messaging</h4>
            <p>The Provider uses SMS for notifications and appointment confirmations.</p>
            <p className="mt-4" style={{ textIndent: '2em', textAlign: 'justify' }}>
              You can cancel the SMS service at any time by replying &quot;STOP&quot; to the number. Upon
              sending &quot;STOP,&quot; we will confirm your unsubscribe status via SMS. Following this
              confirmation, you will no longer receive SMS messages from us. To rejoin, sign up as you
              did initially or text the same number with &quot;Subscribe,&quot; and we will resume sending SMS
              messages to you.
            </p>
            <p className="mt-4" style={{ textIndent: '2em', textAlign: 'justify' }}>
              If you experience issues with the messaging program, reply with the keyword &quot;HELP&quot; for
              more assistance, or reach out <NAME_EMAIL> or call the number from the
              SMS.
            </p>
            <p className="mt-4" style={{ textIndent: '2em' }}>
              Carriers are not liable for delayed or undelivered messages. Message and data rates may
              apply for messages sent to you from us and to us from you. You will receive a message
              for each notification. If you have questions about your text plan or data plan, it is
              best to contact your wireless provider.
            </p>
          </Section>
          <Section title="7. Limitation of Liability">
            <p style={{ textIndent: '2em', textAlign: 'justify' }}>
              To the fullest extent permitted by law, the Provider will not be liable for any
              indirect, incidental, special, consequential, or punitive damages, or for any loss of
              data, revenue, profits, or business opportunities arising out of or related to the use
              of the service, even if we were advised of the possibility of such damages. Our total
              liability under this Agreement is limited to the amount paid by you to the Provider
              during the twelve (12) months preceding the event giving rise to the claim.
            </p>
          </Section>
          <Section title="8. Modifications to Terms">
            <p style={{ textIndent: '2em', textAlign: 'justify' }}>
              The Provider reserves the right to modify this Agreement at any time. When changes are
              made, we will notify merchants through email or by posting the updated terms on our
              website. Continued use of the service after such notice constitutes acceptance of the
              modified terms.
            </p>
          </Section>
          <Section title="9. Termination of Service by the Provider">
            <p style={{ textIndent: '2em', textAlign: 'justify' }}>
              The Provider may terminate this Agreement at its discretion, including for violations of
              these terms or for non-payment of fees. We will provide reasonable notice before
              terminating service unless the breach requires immediate termination (e.g., illegal
              activity or misuse of the service).
            </p>
          </Section>
          <Section title="10. Dispute Resolution and Arbitration">
            <p style={{ textIndent: '2em', textAlign: 'justify' }}>
              Any disputes arising out of or relating to this Agreement shall be governed by the laws
              of the State of Georgia, without regard to its conflict of law principles. The parties
              agree to resolve any disputes through binding arbitration, administered by the American
              Arbitration Association, in accordance with its rules. The arbitration will take place
              in Georgia. The decision of the arbitrator shall be final and binding, and judgment on
              the award may be entered in any court of competent jurisdiction. Any disputes shall be
              resolved on an individual basis, and no class actions or representative actions are
              permitted.
            </p>
          </Section>
          <Section title="11. Governing Law and Jurisdiction">
            <p style={{ textIndent: '2em', textAlign: 'justify' }}>
              This Agreement shall be governed by and construed in accordance with the laws of the
              State of Georgia. Any legal action arising from this Agreement that is not resolved
              through arbitration shall be brought in the courts located in Georgia, and you expressly
              consent to the jurisdiction of such courts.
            </p>
          </Section>
        </CardContent>
      </Card>
    </div>
  );
};
