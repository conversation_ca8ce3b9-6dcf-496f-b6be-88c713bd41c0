'use client';

import { useEffect, useState } from 'react';
import { AUTHSTORE } from '@/lib/auth-storage';

interface IframeAuthHandlerProps {
  children: React.ReactNode;
  onAuthChange?: (isAuthenticated: boolean) => void;
}

/**
 * Component that handles authentication state changes and iframe communication
 * Wrap your app with this component to enable iframe authentication features
 */
export function IframeAuthHandler({ children, onAuthChange }: IframeAuthHandlerProps) {
  useEffect(() => {
    // Check initial authentication status
    const isAuthenticated = AUTHSTORE.isAuthenticated();
    
    // Notify parent if in iframe
    if (AUTHSTORE.isInIframe()) {
      AUTHSTORE.notifyParent(isAuthenticated ? 'authenticated' : 'unauthenticated');
    }
    
    // Call callback if provided
    if (onAuthChange) {
      onAuthChange(isAuthenticated);
    }

    // Listen for authentication changes (storage events)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'health-token') {
        const newIsAuthenticated = !!e.newValue;
        
        // Notify parent if in iframe
        if (AUTHSTORE.isInIframe()) {
          AUTHSTORE.notifyParent(newIsAuthenticated ? 'authenticated' : 'unauthenticated');
        }
        
        // Call callback if provided
        if (onAuthChange) {
          onAuthChange(newIsAuthenticated);
        }
      }
    };

    // Listen for cookie changes (using a polling approach since there's no cookie change event)
    let lastTokenValue = AUTHSTORE.get();
    const checkCookieChanges = () => {
      const currentTokenValue = AUTHSTORE.get();
      if (currentTokenValue !== lastTokenValue) {
        lastTokenValue = currentTokenValue;
        const newIsAuthenticated = !!currentTokenValue;
        
        // Notify parent if in iframe
        if (AUTHSTORE.isInIframe()) {
          AUTHSTORE.notifyParent(newIsAuthenticated ? 'authenticated' : 'unauthenticated');
        }
        
        // Call callback if provided
        if (onAuthChange) {
          onAuthChange(newIsAuthenticated);
        }
      }
    };

    // Set up listeners
    window.addEventListener('storage', handleStorageChange);
    const cookieCheckInterval = setInterval(checkCookieChanges, 1000); // Check every second

    // Listen for messages from parent window (if needed)
    const handleMessage = (event: MessageEvent) => {
      // Validate origin against trusted domains
      if (!AUTHSTORE.isTrustedOrigin(event.origin)) {
        console.warn('Ignored message from untrusted origin:', event.origin);
        return;
      }

      // Handle trusted messages
      if (event.data?.type === 'request-auth-status') {
        const isAuthenticated = AUTHSTORE.isAuthenticated();
        AUTHSTORE.notifyParent(isAuthenticated ? 'authenticated' : 'unauthenticated');
      }
    };

    window.addEventListener('message', handleMessage);

    // Cleanup
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('message', handleMessage);
      clearInterval(cookieCheckInterval);
    };
  }, [onAuthChange]);

  return <>{children}</>;
}

/**
 * Hook to get current authentication status and listen for changes
 */
export function useAuthStatus() {
  const [isAuthenticated, setIsAuthenticated] = useState(AUTHSTORE.isAuthenticated());

  useEffect(() => {
    const checkAuth = () => {
      setIsAuthenticated(AUTHSTORE.isAuthenticated());
    };

    // Check initially
    checkAuth();

    // Listen for storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'health-token') {
        checkAuth();
      }
    };

    // Listen for cookie changes
    const cookieCheckInterval = setInterval(checkAuth, 1000);

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(cookieCheckInterval);
    };
  }, []);

  return {
    isAuthenticated,
    isInIframe: AUTHSTORE.isInIframe(),
    logout: () => {
      AUTHSTORE.clear();
      AUTHSTORE.notifyParent('logout');
      setIsAuthenticated(false);
    },
  };
}
