# Auth Frontend Modifications for Redirect Support

## 🎯 Goal
Modify the auth frontend (`https://ng-auth-fe-dev.dev1.ngnair.com`) to properly handle redirect parameters without needing backend changes.

## 🔧 Required Changes

### 1. Update Login Page JavaScript

Replace the existing login form JavaScript with this enhanced version:

```javascript
// Enhanced login form handler with redirect support
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form') || document.querySelector('form');
    
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const email = formData.get('email');
            const password = formData.get('password');
            
            // Get redirect URL from current page URL
            const urlParams = new URLSearchParams(window.location.search);
            const redirectUrl = urlParams.get('redirect');
            
            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Signing in...';
            submitButton.disabled = true;
            
            try {
                // Call the login API
                const response = await fetch('https://ng-auth-dev.dev1.ngnair.com/api/v1/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('Login successful:', result);
                    
                    // Store authentication data in localStorage and cookies
                    if (result.tokens || result.token) {
                        const tokens = result.tokens || { auth_token: result.token };
                        
                        // Store in localStorage
                        localStorage.setItem('auth_token', tokens.auth_token);
                        if (tokens.refresh_token) {
                            localStorage.setItem('refresh_token', tokens.refresh_token);
                        }
                        if (tokens.device_id) {
                            localStorage.setItem('device_id', tokens.device_id);
                        }
                        if (result.user) {
                            localStorage.setItem('user_data', JSON.stringify(result.user));
                        }
                        
                        // Set cookies for cross-domain access
                        const domain = '.ngnair.com';
                        const expires = new Date();
                        expires.setDate(expires.getDate() + 7); // 7 days
                        
                        document.cookie = `auth_token=${tokens.auth_token}; Domain=${domain}; Path=/; Expires=${expires.toUTCString()}; Secure; SameSite=none`;
                        
                        if (tokens.device_id) {
                            document.cookie = `device_id=${tokens.device_id}; Domain=${domain}; Path=/; Expires=${expires.toUTCString()}; Secure; SameSite=none`;
                        }
                    }
                    
                    // Handle redirect
                    if (redirectUrl) {
                        try {
                            // Validate the redirect URL for security
                            const decodedUrl = decodeURIComponent(redirectUrl);
                            const url = new URL(decodedUrl);
                            
                            // List of allowed domains
                            const allowedDomains = [
                                'localhost',
                                '127.0.0.1',
                                'localhost:3000',
                                'localhost:3001',
                                'localhost:3002',
                                'ng-support-fe-dev.dev1.ngnair.com',
                                'ng-account-fe-dev.dev1.ngnair.com',
                                'ng-billing-fe-dev.dev1.ngnair.com',
                                'ng-auth-fe-dev.dev1.ngnair.com'
                            ];
                            
                            // Check if domain is allowed
                            const isAllowed = allowedDomains.some(domain => 
                                url.hostname === domain || url.hostname.endsWith('.' + domain)
                            );
                            
                            if (isAllowed) {
                                console.log('Redirecting to:', decodedUrl);
                                window.location.href = decodedUrl;
                            } else {
                                console.warn('Redirect URL not allowed:', decodedUrl);
                                showMessage('Login successful! Redirecting...', 'success');
                                setTimeout(() => {
                                    window.location.href = '/';
                                }, 2000);
                            }
                        } catch (error) {
                            console.error('Invalid redirect URL:', error);
                            showMessage('Login successful! Redirecting...', 'success');
                            setTimeout(() => {
                                window.location.href = '/';
                            }, 2000);
                        }
                    } else {
                        // No redirect URL, show success and redirect to home
                        showMessage('Login successful! Redirecting...', 'success');
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 2000);
                    }
                    
                } else {
                    const error = await response.json().catch(() => ({}));
                    const errorMessage = error.message || error.error || 'Login failed. Please check your credentials.';
                    showMessage(errorMessage, 'error');
                }
                
            } catch (error) {
                console.error('Login error:', error);
                showMessage('Login failed. Please check your connection and try again.', 'error');
            } finally {
                // Reset button state
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }
        });
    }
    
    // Helper function to show messages
    function showMessage(message, type = 'info') {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.auth-message');
        existingMessages.forEach(msg => msg.remove());
        
        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `auth-message ${type}`;
        messageDiv.style.cssText = `
            padding: 12px 16px;
            margin: 16px 0;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
        `;
        
        // Set colors based on type
        if (type === 'success') {
            messageDiv.style.backgroundColor = '#d1fae5';
            messageDiv.style.color = '#065f46';
            messageDiv.style.border = '1px solid #a7f3d0';
        } else if (type === 'error') {
            messageDiv.style.backgroundColor = '#fee2e2';
            messageDiv.style.color = '#991b1b';
            messageDiv.style.border = '1px solid #fca5a5';
        } else {
            messageDiv.style.backgroundColor = '#dbeafe';
            messageDiv.style.color = '#1e40af';
            messageDiv.style.border = '1px solid #93c5fd';
        }
        
        messageDiv.textContent = message;
        
        // Insert message after the form
        const form = document.getElementById('login-form') || document.querySelector('form');
        if (form) {
            form.parentNode.insertBefore(messageDiv, form.nextSibling);
        }
        
        // Auto-remove success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 5000);
        }
    }
    
    // Show redirect URL if present
    const urlParams = new URLSearchParams(window.location.search);
    const redirectUrl = urlParams.get('redirect');
    if (redirectUrl) {
        try {
            const decodedUrl = decodeURIComponent(redirectUrl);
            showMessage(`After login, you will be redirected to: ${decodedUrl}`, 'info');
        } catch (error) {
            console.error('Invalid redirect URL in parameters');
        }
    }
});
```

### 2. Add CSS for Messages (Optional)

Add this CSS to improve the message styling:

```css
.auth-message {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
```

### 3. Update HTML Form (if needed)

Make sure the login form has the correct structure:

```html
<form id="login-form" method="post">
    <div>
        <label for="email">Email address</label>
        <input type="email" id="email" name="email" required>
    </div>
    
    <div>
        <label for="password">Password</label>
        <input type="password" id="password" name="password" required>
    </div>
    
    <button type="submit">Sign in</button>
</form>
```

## 🧪 Testing the Changes

### 1. Test URLs to Try:

```
# Return to localhost:3000 (main microservice)
https://ng-auth-fe-dev.dev1.ngnair.com/login?redirect=http%3A//localhost:3000/

# Return to localhost:3001 (secondary microservice)
https://ng-auth-fe-dev.dev1.ngnair.com/login?redirect=http%3A//localhost:3001/

# Return to localhost:3002 (third microservice)
https://ng-auth-fe-dev.dev1.ngnair.com/login?redirect=http%3A//localhost:3002/

# Go to support dashboard
https://ng-auth-fe-dev.dev1.ngnair.com/login?redirect=https%3A//ng-support-fe-dev.dev1.ngnair.com/dashboard

# Custom page with query params
https://ng-auth-fe-dev.dev1.ngnair.com/login?redirect=http%3A//localhost:3000/dashboard%3Fwelcome%3Dtrue
```

### 2. Expected Behavior:

1. **Load login page** → Shows message about where you'll be redirected
2. **Enter credentials** → `<EMAIL>` / `Walking&Forward37`
3. **Submit form** → Shows "Signing in..." loading state
4. **Success** → Shows success message and redirects to target URL
5. **Target page** → Should have auth tokens in localStorage and cookies

### 3. Debug in Browser Console:

```javascript
// Check if auth data was stored
console.log('Auth Token:', localStorage.getItem('auth_token'));
console.log('User Data:', localStorage.getItem('user_data'));
console.log('Cookies:', document.cookie);
```

## 🔒 Security Features

The code includes several security measures:

1. **URL Validation** → Only allows redirects to approved domains
2. **URL Decoding** → Safely handles encoded URLs
3. **Error Handling** → Graceful fallbacks for invalid URLs
4. **Domain Restriction** → Prevents open redirect vulnerabilities

## 📝 Implementation Steps

1. **Access the auth frontend codebase**
2. **Find the login page JavaScript file**
3. **Replace the form submission handler** with the code above
4. **Test with the demo credentials**
5. **Verify redirects work correctly**

This solution works entirely within the frontend and doesn't require any backend API changes!
