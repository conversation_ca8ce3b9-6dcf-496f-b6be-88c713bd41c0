# === Stage 1: Builder ===
FROM node:20-alpine AS builder

WORKDIR /app

# Install deps only
COPY package.json package-lock.json ./
RUN npm ci

# Copy full source
COPY . .

# Build-time environment variables (will be passed from CapRover)
ARG NEXT_PUBLIC_APP_ENV
ARG NEXT_PUBLIC_GRAPHQL_URL
ARG AUTH_URL
ARG NEXT_PUBLIC_COOKIE_DOMAIN
ARG NEXT_PUBLIC_TRUSTED_DOMAINS
ARG NEXT_PUBLIC_FALLBACK_URL
ARG NEXT_PUBLIC_AUTH_SERVICE_URL
ARG NEXT_PUBLIC_APP_URL

# Set environment variables for build
ENV NEXT_PUBLIC_APP_ENV=$NEXT_PUBLIC_APP_ENV
ENV NEXT_PUBLIC_GRAPHQL_URL=$NEXT_PUBLIC_GRAPHQL_URL
ENV AUTH_URL=$AUTH_URL
ENV NEXT_PUBLIC_COOKIE_DOMAIN=$NEXT_PUBLIC_COOKIE_DOMAIN
ENV NEXT_PUBLIC_TRUSTED_DOMAINS=$NEXT_PUBLIC_TRUSTED_DOMAINS
ENV NEXT_PUBLIC_FALLBACK_URL=$NEXT_PUBLIC_FALLBACK_URL
ENV NEXT_PUBLIC_AUTH_SERVICE_URL=$NEXT_PUBLIC_AUTH_SERVICE_URL
ENV NEXT_PUBLIC_APP_URL=$NEXT_PUBLIC_APP_URL

# Build the Next.js app
RUN npm run build

# === Stage 2: Production Image ===
FROM node:20-alpine AS runner

WORKDIR /app

ENV NODE_ENV=production

# Runtime environment variables will be set by CapRover
# No default values to allow CapRover to override them

# Copy the minimal production artifacts from the builder
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

EXPOSE 3001

# Start the app
# Use "start" for production mode, "dev" for development mode
CMD ["npm", "start"]
