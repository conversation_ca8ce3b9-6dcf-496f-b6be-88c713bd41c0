# Frontend Integration Guide - NGnair Auth System

## 🎯 Overview

This guide explains how other frontend applications in the NGnair ecosystem can integrate with the centralized authentication frontend for seamless user authentication and registration.

## 🔗 Integration Architecture

### Authentication Flow
```
User Frontend → Auth Frontend → Backend API → User Frontend
     ↓              ↓              ↓              ↓
  Redirect      Login/Register   Validate     Redirect Back
   to Auth        Process       Credentials   with Token
```

### Key Components
- **Auth Frontend**: `https://ng-auth-dev.dev1.ngnair.com` (Centralized authentication UI)
- **Backend API**: `https://ng-auth-dev.dev1.ngnair.com/api/v1` (Authentication services)
- **Your Frontend**: Your microservice frontend application

## 🚀 Quick Start Integration

### 1. **Redirect to Auth Frontend for Login**

When users need to authenticate, redirect them to the auth frontend with your return URL:

```javascript
// Redirect to login
const redirectToLogin = () => {
  const authBaseUrl = 'https://ng-auth-dev.dev1.ngnair.com';
  const returnUrl = encodeURIComponent(window.location.origin + '/dashboard');
  
  window.location.href = `${authBaseUrl}/login?redirect=${returnUrl}`;
};

// Redirect to registration
const redirectToRegister = () => {
  const authBaseUrl = 'https://ng-auth-dev.dev1.ngnair.com';
  const returnUrl = encodeURIComponent(window.location.origin + '/dashboard');
  
  window.location.href = `${authBaseUrl}/register?redirect=${returnUrl}`;
};
```

### 2. **Handle Return from Auth Frontend**

After successful authentication, users will be redirected back to your application:

```javascript
// Check for authentication on page load
useEffect(() => {
  const checkAuthentication = () => {
    // Check for authentication token in cookies
    const token = getCookie('health-token'); // httpOnly cookie set by backend
    
    if (token) {
      // User is authenticated
      console.log('User authenticated');
      // Proceed with authenticated user flow
    } else {
      // User not authenticated, redirect to login
      redirectToLogin();
    }
  };
  
  checkAuthentication();
}, []);

// Helper function to read cookies
const getCookie = (name) => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(';').shift();
  return null;
};
```

## 📋 URL Parameters and Patterns

### Required Parameters

#### **`redirect` Parameter**
- **Purpose**: Specifies where to redirect after successful authentication
- **Format**: Fully qualified URL (must be trusted domain)
- **Example**: `https://ng-account-fe-dev.dev1.ngnair.com/dashboard`

```javascript
// Correct usage
const redirectUrl = 'https://ng-account-fe-dev.dev1.ngnair.com/dashboard';
const authUrl = `https://ng-auth-dev.dev1.ngnair.com/login?redirect=${encodeURIComponent(redirectUrl)}`;

// With additional parameters
const redirectUrl = 'https://ng-account-fe-dev.dev1.ngnair.com/dashboard?tab=settings';
const authUrl = `https://ng-auth-dev.dev1.ngnair.com/login?redirect=${encodeURIComponent(redirectUrl)}`;
```

### Trusted Domain Patterns

Your redirect URLs must match trusted domain patterns:

✅ **Allowed Domains:**
- `ng-*.dev*.ngnair.com` (e.g., `ng-account-fe-dev.dev1.ngnair.com`)
- `ng-*.ngnair.com` (e.g., `ng-support.ngnair.com`)
- `localhost:*` (development only)

❌ **Blocked Domains:**
- External domains (e.g., `google.com`)
- Non-HTTPS in production
- Malicious patterns (e.g., `javascript:`)

## 🔐 Authentication Token Handling

### Cookie-Based Authentication (Recommended)

The auth system uses httpOnly cookies for security:

```javascript
// The backend automatically sets httpOnly cookies
// Your frontend doesn't need to handle tokens directly

// Check authentication status
const isAuthenticated = () => {
  // Make a request to a protected endpoint
  return fetch('/api/user/profile', {
    credentials: 'include' // Include cookies
  })
  .then(response => response.ok)
  .catch(() => false);
};

// Make authenticated requests
const fetchUserData = async () => {
  const response = await fetch('/api/user/data', {
    credentials: 'include' // Always include cookies
  });
  
  if (response.status === 401) {
    // Token expired, redirect to login
    redirectToLogin();
    return;
  }
  
  return response.json();
};
```

### JWT Token Validation (Advanced)

If you need to validate tokens client-side:

```javascript
// Decode JWT payload (for display purposes only)
const decodeJWT = (token) => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload;
  } catch (error) {
    console.error('Invalid JWT token');
    return null;
  }
};

// Check token expiration
const isTokenExpired = (token) => {
  const payload = decodeJWT(token);
  if (!payload || !payload.exp) return true;
  
  return Date.now() >= payload.exp * 1000;
};
```

## 🔄 Complete Integration Example

### React Integration

```jsx
import React, { useEffect, useState } from 'react';

const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      // Check if user is authenticated by making a request to protected endpoint
      const response = await fetch('/api/user/profile', {
        credentials: 'include'
      });
      
      setIsAuthenticated(response.ok);
    } catch (error) {
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const login = () => {
    const authBaseUrl = 'https://ng-auth-dev.dev1.ngnair.com';
    const returnUrl = encodeURIComponent(window.location.origin + '/dashboard');
    window.location.href = `${authBaseUrl}/login?redirect=${returnUrl}`;
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsAuthenticated(false);
      // Redirect to login
      login();
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return (
      <div>
        <h1>Please log in</h1>
        <button onClick={login}>Login</button>
      </div>
    );
  }

  return (
    <div>
      {children}
      <button onClick={logout}>Logout</button>
    </div>
  );
};

export default AuthProvider;
```

### Vue.js Integration

```javascript
// auth.js
export const authService = {
  checkAuth: async () => {
    try {
      const response = await fetch('/api/user/profile', {
        credentials: 'include'
      });
      return response.ok;
    } catch {
      return false;
    }
  },

  login: () => {
    const authBaseUrl = 'https://ng-auth-dev.dev1.ngnair.com';
    const returnUrl = encodeURIComponent(window.location.origin + '/dashboard');
    window.location.href = `${authBaseUrl}/login?redirect=${returnUrl}`;
  },

  register: () => {
    const authBaseUrl = 'https://ng-auth-dev.dev1.ngnair.com';
    const returnUrl = encodeURIComponent(window.location.origin + '/dashboard');
    window.location.href = `${authBaseUrl}/register?redirect=${returnUrl}`;
  },

  logout: async () => {
    await fetch('/api/auth/logout', {
      method: 'POST',
      credentials: 'include'
    });
    authService.login();
  }
};

// In your Vue component
export default {
  data() {
    return {
      isAuthenticated: false,
      isLoading: true
    };
  },
  
  async mounted() {
    this.isAuthenticated = await authService.checkAuth();
    this.isLoading = false;
  },
  
  methods: {
    login: authService.login,
    logout: authService.logout
  }
};
```

## 🛡️ Security Considerations

### 1. **HTTPS Only**
- Always use HTTPS in production
- HTTP only allowed for localhost in development

### 2. **Domain Validation**
- Only trusted domains are allowed for redirects
- Validate redirect URLs on both frontend and backend

### 3. **Cookie Security**
- Authentication cookies are httpOnly and secure
- Include `credentials: 'include'` in all API requests

### 4. **Token Expiration**
- Handle 401 responses by redirecting to login
- Implement automatic token refresh if needed

## 🧪 Testing Your Integration

### 1. **Test Authentication Flow**
```javascript
// Test login redirect
const testLogin = () => {
  console.log('Testing login redirect...');
  const authUrl = 'https://ng-auth-dev.dev1.ngnair.com/login?redirect=' + 
    encodeURIComponent('http://localhost:3000/test');
  window.open(authUrl, '_blank');
};

// Test registration redirect
const testRegister = () => {
  console.log('Testing registration redirect...');
  const authUrl = 'https://ng-auth-dev.dev1.ngnair.com/register?redirect=' + 
    encodeURIComponent('http://localhost:3000/test');
  window.open(authUrl, '_blank');
};
```

### 2. **Test Protected Endpoints**
```javascript
const testProtectedEndpoint = async () => {
  try {
    const response = await fetch('/api/protected', {
      credentials: 'include'
    });
    
    if (response.ok) {
      console.log('✅ Authentication working');
    } else if (response.status === 401) {
      console.log('❌ Not authenticated');
    }
  } catch (error) {
    console.error('❌ Request failed:', error);
  }
};
```

## 📞 Support and Troubleshooting

### Common Issues

1. **Redirect not working**: Check domain is in trusted patterns
2. **Authentication not persisting**: Ensure `credentials: 'include'` in requests
3. **CORS errors**: Verify backend CORS configuration includes your domain

### Debug Mode

Enable debug logging in development:
```javascript
// Add to your app initialization
if (process.env.NODE_ENV === 'development') {
  window.authDebug = true;
}
```

### Getting Help

- Check browser console for error messages
- Verify network requests include cookies
- Test redirect URLs manually
- Contact the auth team with specific error details
