import Cookies from 'js-cookie';

// Get trusted domain patterns from environment or use defaults
const getDefaultTrustedDomains = (): string[] => {
  const envDomains = process.env.NEXT_PUBLIC_TRUSTED_DOMAINS;

  if (envDomains) {
    return envDomains.split(',').map(domain => domain.trim()).filter(Boolean);
  }

  // Default patterns if no environment variable is set
  return [
    'ng-*-fe-*.dev*.ngnair.com',     // Matches ng-support-fe-dev.dev1.ngnair.com
    'ng-*.ng-*.ngnair.com',          // Matches ng-ob.ng-dev.ngnair.com
    'ng-*.dev*.ngnair.com',          // General pattern for ng-[service].dev[env].ngnair.com
    'ng-*-fe-*.ngnair.com',          // General pattern for ng-[service]-fe-[env].ngnair.com
    '*.ngnair.com',                  // All subdomains of ngnair.com
    'ngnair.com',                    // Exact match
    'localhost',                     // Development
    '127.0.0.1',                     // Local development
  ];
};

// Trusted domain patterns for iframe communication
const TRUSTED_DOMAIN_PATTERNS = getDefaultTrustedDomains();

// Cookie configuration for iframe compatibility
const COOKIE_OPTIONS = {
  expires: 7, // 7 days
  secure: process.env.NODE_ENV === 'production', // Only secure in production
  sameSite: 'none' as const , // Always use 'none' for cross-origin iframes
  domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN || undefined, // Set domain if needed
};

const SESSION_COOKIE_OPTIONS = {
  expires: 1, // 1 day for session ID
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'none' as const, // Always use 'none'
  domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN || undefined,
};

// Domain validation functions
const isValidDomain = (domain: string, pattern: string): boolean => {
  // Convert wildcard pattern to regex
  const regexPattern = pattern
    .replace(/\./g, '\\.')  // Escape dots
    .replace(/\*/g, '[^.]*'); // Replace * with non-dot characters

  const regex = new RegExp(`^${regexPattern}$`, 'i');
  return regex.test(domain);
};

const isTrustedDomain = (origin: string): boolean => {
  try {
    const url = new URL(origin);
    const domain = url.hostname;
    const host = url.host; // hostname + port

    // Check against trusted patterns
    return TRUSTED_DOMAIN_PATTERNS.some(pattern => {
      // If pattern includes a port (contains :), check against full host
      if (pattern.includes(':')) {
        return isValidDomain(host, pattern);
      }
      // Otherwise, check against hostname only
      return isValidDomain(domain, pattern);
    });
  } catch (e) {
    console.warn('Invalid origin URL:', origin);
    return false;
  }
};

// Get trusted origins for postMessage (for development, allow all in dev mode)
const getTrustedOrigins = (): string[] => {
  if (process.env.NODE_ENV === 'development') {
    return ['*']; // Allow all in development
  }

  // In production, generate specific origins from patterns
  const origins: string[] = [];
  TRUSTED_DOMAIN_PATTERNS.forEach(pattern => {
    if (!pattern.includes('*')) {
      // Exact domain - add both http and https
      origins.push(`https://${pattern}`);
      origins.push(`http://${pattern}`);
    }
  });

  return origins.length > 0 ? origins : ['*'];
};

export const AUTHSTORE = {
  get: () => {
    if (typeof window !== 'undefined') {
      // Try cookie first, fallback to localStorage for backward compatibility
      const cookieToken = Cookies.get('health-token');
      if (cookieToken) {
        return cookieToken;
      }

      // Fallback to localStorage and migrate to cookie
      const localToken = localStorage?.getItem('health-token');
      if (localToken) {
        // Migrate from localStorage to cookie
        Cookies.set('health-token', localToken, COOKIE_OPTIONS);
        localStorage?.removeItem('health-token');
        return localToken;
      }

      return '';
    }
    return '';
  },

  set: (token: string) => {
    if (typeof window !== 'undefined') {
      // Set both cookie and localStorage for maximum compatibility
      Cookies.set('health-token', token, COOKIE_OPTIONS);
      localStorage?.setItem('health-token', token);
    }
  },

  clear: () => {
    if (typeof window !== 'undefined') {
      // Clear both cookie and localStorage
      Cookies.remove('health-token', {
        secure: COOKIE_OPTIONS.secure,
        sameSite: COOKIE_OPTIONS.sameSite,
        domain: COOKIE_OPTIONS.domain,
      });
      localStorage?.removeItem('health-token');
    }
  },

  session: () => {
    if (typeof window !== 'undefined') {
      // Enhanced session management with cookie support
      const hasToken = AUTHSTORE.get();
      const currentPath = window.location.pathname;

      // Don't redirect if we're already on the right page
      if (hasToken && currentPath === '/login') {
        window.location.href = '/dashboard';
      } else if (!hasToken && currentPath !== '/login' && !currentPath.startsWith('/register') && !currentPath.startsWith('/forgot-password') && !currentPath.startsWith('/reset-password')) {
        window.location.href = '/login';
      }
    }
  },

  // Additional utility methods for iframe scenarios
  isAuthenticated: () => {
    return !!AUTHSTORE.get();
  },

  // Method to check if running in iframe
  isInIframe: () => {
    if (typeof window !== 'undefined') {
      try {
        return window.self !== window.top;
      } catch (e) {
        return true; // If we can't access window.top, we're likely in an iframe
      }
    }
    return false;
  },

  // Method to post authentication status to parent window (for iframe scenarios)
  notifyParent: (status: 'authenticated' | 'unauthenticated' | 'login-success' | 'logout') => {
    if (typeof window !== 'undefined' && AUTHSTORE.isInIframe()) {
      try {
        const trustedOrigins = getTrustedOrigins();

        // Send to each trusted origin
        trustedOrigins.forEach(origin => {
          window.parent.postMessage({
            type: 'auth-status',
            status,
            timestamp: Date.now(),
            domain: window.location.hostname, // Include our domain for verification
          }, origin);
        });

        console.log(`Notified parent window(s) of auth status: ${status}`);
      } catch (e) {
        console.warn('Could not notify parent window:', e);
      }
    }
  },

  // Method to validate if a message origin is trusted
  isTrustedOrigin: (origin: string): boolean => {
    return isTrustedDomain(origin);
  },

  // Method to get trusted domain patterns (for external use)
  getTrustedPatterns: (): string[] => {
    return [...TRUSTED_DOMAIN_PATTERNS];
  },
};
