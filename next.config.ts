/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  output: "standalone",
  env: {
    AUTH_URL: process.env.AUTH_URL,
  },
  // Disable caching in development to prevent stale JavaScript
  ...(process.env.NODE_ENV === 'development' && {
    generateEtags: false,
    poweredByHeader: false,
  }),
  async redirects() {
    return [
      {
        source: "/",
        destination: "/login",
        permanent: false,
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: "/api/auth/:path*",
        destination:
          process.env.AUTH_URL || "http://localhost:3000/api/auth/:path*",
      },
    ];
  },
};

export default nextConfig;
