'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { handleRedirect, validateRedirectUrl, extractRedirectUrl } from '@/lib/redirect-handler';

/**
 * Development-only component for testing redirect functionality
 * Only shows in development mode
 */
export default function RedirectTester() {
  const [testUrl, setTestUrl] = useState('');
  const [validationResult, setValidationResult] = useState<any>(null);
  const [extractedInfo, setExtractedInfo] = useState<any>(null);

  // Only show in development
  if (process.env.NEXT_PUBLIC_APP_ENV !== 'development') {
    return null;
  }

  const handleValidateUrl = () => {
    if (!testUrl) return;
    
    const result = validateRedirectUrl(testUrl);
    setValidationResult(result);
    console.log('Validation result:', result);
  };

  const handleExtractInfo = () => {
    const extracted = extractRedirectUrl();
    setExtractedInfo(extracted);
    console.log('Extracted redirect info:', extracted);
  };

  const handleTestRedirect = () => {
    if (!testUrl) return;
    
    console.log('Testing redirect to:', testUrl);
    handleRedirect(testUrl);
  };

  const handleCurrentPageRedirect = () => {
    console.log('Testing redirect with current page URL parameters');
    handleRedirect();
  };

  return (
    <Card className="fixed bottom-4 right-4 w-96 z-50 border-2 border-blue-500 bg-blue-50">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm text-blue-700">
          🧪 Redirect Tester (Dev Only)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="space-y-2">
          <Input
            placeholder="Enter redirect URL to test"
            value={testUrl}
            onChange={(e) => setTestUrl(e.target.value)}
            className="text-xs"
          />
          
          <div className="grid grid-cols-2 gap-2">
            <Button 
              onClick={handleValidateUrl}
              size="sm"
              variant="outline"
              className="text-xs"
            >
              Validate URL
            </Button>
            
            <Button 
              onClick={handleTestRedirect}
              size="sm"
              className="text-xs"
              disabled={!testUrl}
            >
              Test Redirect
            </Button>
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            <Button 
              onClick={handleExtractInfo}
              size="sm"
              variant="outline"
              className="text-xs"
            >
              Extract Current
            </Button>
            
            <Button 
              onClick={handleCurrentPageRedirect}
              size="sm"
              className="text-xs"
            >
              Redirect Current
            </Button>
          </div>
        </div>

        {validationResult && (
          <div className="text-xs p-2 bg-white rounded border">
            <div className="font-semibold">Validation Result:</div>
            <div className={validationResult.isValid ? 'text-green-600' : 'text-red-600'}>
              {validationResult.isValid ? '✅ Valid' : '❌ Invalid'}
            </div>
            {validationResult.reason && (
              <div className="text-gray-600 mt-1">{validationResult.reason}</div>
            )}
          </div>
        )}

        {extractedInfo && (
          <div className="text-xs p-2 bg-white rounded border">
            <div className="font-semibold">Extracted Info:</div>
            <div>URL: {extractedInfo.url || 'None'}</div>
            <div>Source: {extractedInfo.source}</div>
          </div>
        )}

        <div className="text-xs text-gray-500 border-t pt-2">
          <div>Current URL: {typeof window !== 'undefined' ? window.location.href : 'N/A'}</div>
          <div>Check console for detailed logs</div>
        </div>
      </CardContent>
    </Card>
  );
}
