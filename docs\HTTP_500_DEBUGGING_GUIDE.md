# HTTP 500 Error Debugging Guide - Set Password Page

## Overview
This guide helps debug HTTP 500 errors occurring during account creation on the set-password page.

## 🔍 Step 1: Browser-Side Investigation

### Check Browser Network Tab
1. Open browser Developer Tools (F12)
2. Go to **Network** tab
3. Clear existing requests
4. Try to create an account with a strong password
5. Look for the failing request to `/api/v1/set_password`

**What to check:**
- **Request URL**: Should be `https://ng-auth-dev.dev1.ngnair.com/api/v1/set_password`
- **Request Method**: Should be `POST`
- **Status Code**: Look for `500 Internal Server Error`
- **Request Headers**: Check for `Content-Type: application/json`
- **Request Payload**: Verify it contains `user_id` and `password`

### Check Browser Console
1. Open **Console** tab in Developer Tools
2. Look for detailed error logs (enhanced debugging is now enabled)
3. Check for these debug messages:
   ```
   === SET PASSWORD DEBUG INFO ===
   User ID: [user_id]
   Password length: [length]
   Password strength score: [0-4]
   API Base URL: https://ng-auth-dev.dev1.ngnair.com/api/v1
   ================================
   ```

### Verify Request Payload
The request should contain:
```json
{
  "user_id": "valid-uuid-string",
  "password": "user-entered-password"
}
```

## 🔍 Step 2: Frontend Validation Checks

### Password Strength Validation
1. Ensure password meets frontend requirements:
   - Minimum 8 characters
   - zxcvbn score ≥ 2 (Fair or better)
   - Contains uppercase, lowercase, number, special character

2. Check console for password validation:
   ```javascript
   // Should show score of 2 or higher
   console.log('Password strength score:', zxcvbn(password).score);
   ```

### User ID Validation
1. Verify user_id is present and valid:
   ```javascript
   // Check in browser console
   console.log('User ID:', userInfo.userId);
   ```

2. User ID should be a valid UUID format

## 🔍 Step 3: Backend Investigation

### Check Backend Server Logs
1. **Rails Server Logs**: Look for errors in your Rails application logs
2. **Database Logs**: Check for database connection issues
3. **Authentication Logs**: Verify user exists and is in correct state

### Common Backend Issues

#### 1. User Not Found (404 → 500)
```ruby
# Backend should handle this gracefully
user = User.find(params[:user_id])
# If user doesn't exist, this might cause 500 instead of 404
```

#### 2. Database Connection Issues
- Check database connectivity
- Verify database credentials
- Check for connection pool exhaustion

#### 3. Password Validation Errors
```ruby
# Backend password validation might be failing
# Check if backend uses different zxcvbn requirements
```

#### 4. Missing Required Fields
- Verify backend expects `user_id` and `password` fields
- Check for any additional required fields

## 🔍 Step 4: API Endpoint Verification

### Test API Endpoint Directly
Use curl or Postman to test the endpoint:

```bash
curl -X POST https://ng-auth-dev.dev1.ngnair.com/api/v1/set_password \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "user_id": "your-user-id-here",
    "password": "TestPassword123!"
  }'
```

### Expected Response
**Success (200):**
```json
{
  "access_token": "jwt-token-here",
  "device_id": "device-id-here"
}
```

**Error (500):**
```json
{
  "message": "Internal server error details",
  "error": "Specific error information"
}
```

## 🔧 Step 5: Common Fixes

### 1. Backend User State Issues
Ensure user is in correct state for password setting:
- User should exist in database
- User should be in "email_verified" state
- User should not already have a password set

### 2. Environment Configuration
Verify environment variables:
- `API_BASE_URL` is correct
- Database connection strings are valid
- Authentication secrets are properly configured

### 3. CORS Issues
If seeing CORS errors along with 500:
- Check backend CORS configuration
- Verify allowed origins include your frontend domain

## 🔧 Step 6: Enhanced Debugging (Already Implemented)

The following debugging features are now active in development mode:

### Frontend Debugging
- Detailed request logging before API call
- Enhanced error handling with specific HTTP 500 messages
- Console logging of all relevant data

### API Service Debugging
- Request URL and payload logging
- Response logging
- Detailed error information including backend response

## 📋 Debugging Checklist

- [ ] Check browser Network tab for exact error details
- [ ] Verify request payload contains valid user_id and password
- [ ] Confirm password meets strength requirements (score ≥ 2)
- [ ] Check backend server logs for specific error
- [ ] Verify user exists in database and is in correct state
- [ ] Test API endpoint directly with curl/Postman
- [ ] Check database connectivity and credentials
- [ ] Verify environment configuration
- [ ] Check for CORS issues

## 🚨 Next Steps After Debugging

1. **Identify Root Cause**: Use the debugging information to pinpoint the exact issue
2. **Fix Backend Issue**: Address the specific backend problem causing the 500 error
3. **Test Complete Flow**: Verify the entire registration → verification → password setting flow
4. **Test Redirect**: Ensure successful account creation redirects properly

## 📞 Getting Help

If you need assistance:
1. Collect all debugging information from browser console
2. Gather backend server logs around the time of the error
3. Note the exact steps to reproduce the issue
4. Include the user_id and timestamp of the failed request
