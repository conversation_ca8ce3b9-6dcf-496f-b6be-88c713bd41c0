'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON>,
  CardContent,
  CardHeader,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LockIcon, CheckIcon, Eye, EyeOff } from 'lucide-react';
import { useEffect, useState } from 'react';
import { authService, ApiError } from '@/lib/api/auth-service';
import { toast } from 'sonner';
import PasswordStrengthChecker, { usePasswordStrength } from '@/components/PasswordStrengthChecker';
import zxcvbn from 'zxcvbn';
import { handleRedirect } from '@/lib/redirect-handler';
import Image from 'next/image';
import RedirectTester from '@/components/RedirectTester';

interface PasswordRequirement {
  text: string;
  met: boolean;
}

const PasswordRequirements = ({ password }: { password: string }) => {
  const requirements: PasswordRequirement[] = [
    { text: 'At least 8 characters long', met: password.length >= 8 },
    { text: 'Contains uppercase letter', met: /[A-Z]/.test(password) },
    { text: 'Contains lowercase letter', met: /[a-z]/.test(password) },
    { text: 'Contains a number', met: /\d/.test(password) },
    { text: 'Contains special character', met: /[!@#$%^&*(),.?":{}|<>]/.test(password) },
  ];

  return (
    <div className="space-y-2">
      <p className="text-sm font-medium text-gray-700">Password requirements:</p>
      <ul className="space-y-1">
        {requirements.map((req, index) => (
          <li key={index} className="flex items-center space-x-2 text-xs">
            <div className={`w-4 h-4 rounded-full flex items-center justify-center ${
              req.met ? 'bg-green-500' : 'bg-gray-300'
            }`}>
              {req.met && <CheckIcon className="w-2 h-2 text-white" />}
            </div>
            <span className={req.met ? 'text-green-600' : 'text-gray-500'}>
              {req.text}
            </span>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default function SetPasswordPage() {
  const [form, setForm] = useState({
    password: '',
    confirmPassword: '',
  });
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    // Get user info from URL params or sessionStorage
    const urlParams = new URLSearchParams(window.location.search);
    const userId = urlParams.get('user_id');

    // Try to get from sessionStorage
    const storedData = sessionStorage.getItem('registrationData');

    if (userId && storedData) {
      const data = JSON.parse(storedData);
      if (data.userId === userId) {
        setUserInfo(data);
      } else {
        window.location.href = '/register';
      }
    } else {
      // Redirect to register if no valid info
      window.location.href = '/register';
    }
  }, []);

  useEffect(() => {
    setError('');
  }, [form]);

  usePasswordStrength(form.password); // For consistency with password strength validation

  const validatePassword = (password: string): boolean => {
    // Use zxcvbn for validation - require score of at least 2 (Fair)
    if (password.length < 8) return false;
    const result = zxcvbn(password);
    return result.score >= 2;
  };

  const handleSubmit = async () => {
    if (!form.password || !form.confirmPassword) {
      setError('Please fill in all fields');
      return;
    }

    if (!validatePassword(form.password)) {
      setError('Password is too weak. Please choose a stronger password with a score of at least "Fair" (2/4).');
      return;
    }

    if (form.password !== form.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setSubmitting(true);
    setError('');

    // Enhanced debugging for HTTP 500 investigation
    if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
      console.log('=== SET PASSWORD DEBUG INFO ===');
      console.log('User ID:', userInfo.userId);
      console.log('Password length:', form.password.length);
      console.log('Password strength score:', zxcvbn(form.password).score);
      console.log('API Base URL:', 'https://ng-auth-dev.dev1.ngnair.com/api/v1');
      console.log('Request payload:', {
        user_id: userInfo.userId,
        password: '[REDACTED]'
      });
      console.log('Current URL:', window.location.href);
      console.log('Device ID in localStorage:', localStorage.getItem('device_id'));
      console.log('Device ID in sessionStorage:', sessionStorage.getItem('device_id'));
      console.log('================================');
    }

    try {
      await authService.setPassword({
        user_id: userInfo.userId,
        password: form.password
      });

      // Clear registration data from sessionStorage
      sessionStorage.removeItem('registrationData');

      // Show success toast and redirect
      toast.success('Account created successfully! You are now logged in.', {
        duration: 3000,
      });

      // The access token is automatically stored by the API service
      // Redirect to original microservice or fallback after a short delay
      setTimeout(() => {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Set password successful, initiating redirect...');
          console.log('Current URL:', window.location.href);
          console.log('URL params:', window.location.search);
        }

        // Clear browser history to prevent back navigation to registration flow
        if (window.history.replaceState) {
          window.history.replaceState(null, '', window.location.pathname + window.location.search);
        }

        handleRedirect();
      }, 1500);
    } catch (err) {
      console.error('Set password error:', err);
      const apiError = err as ApiError;

      // Enhanced error logging for debugging HTTP 500
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('=== ERROR DETAILS ===');
        console.log('Error object:', err);
        console.log('Status code:', apiError.status);
        console.log('Error message:', apiError.message);
        console.log('Full error:', JSON.stringify(err, null, 2));
        console.log('====================');
      }

      // Handle specific error codes
      if (apiError.status === 422) {
        const weakPasswordMessage = 'Password is too weak. Please choose a stronger password with a score of at least "Fair" (2/4).';
        setError(weakPasswordMessage);
        toast.error('Password rejected by server - too weak', {
          duration: 4000,
        });
      } else if (apiError.status === 500) {
        // Enhanced HTTP 500 handling - check if password was actually set successfully
        const serverErrorMessage = 'Server error occurred. Checking if password was actually set...';
        setError(serverErrorMessage);

        // Show initial error but also check if the operation actually succeeded
        toast.error('HTTP 500 received - Verifying if password was set successfully...', {
          duration: 4000,
        });

        // Additional debugging for 500 errors
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.error('=== HTTP 500 ERROR ANALYSIS ===');
          console.error('This might be a response format issue, not an actual failure');
          console.error('Backend logs should be checked to verify if password was set');
          console.error('- Check backend server logs for actual success/failure');
          console.error('- Verify API endpoint: /api/v1/set_password');
          console.error('- Confirm user_id exists in database');
          console.error('- Check password validation on backend');
          console.error('- Verify database connectivity');
          console.error('==============================');
        }

        // Give user option to continue if they believe the password was set
        setTimeout(() => {
          const shouldContinue = window.confirm(
            'A server error occurred, but your password might have been set successfully. ' +
            'Would you like to try logging in with your new password? ' +
            '(Click Cancel to try setting the password again)'
          );

          if (shouldContinue) {
            // Clear the error and redirect to login
            setError('');
            toast.info('Redirecting to login page...', { duration: 2000 });

            setTimeout(() => {
              // Redirect to login with the same redirect parameter
              const urlParams = new URLSearchParams(window.location.search);
              const redirectUrl = urlParams.get('redirect');
              const loginUrl = redirectUrl
                ? `/login?redirect=${encodeURIComponent(redirectUrl)}`
                : '/login';

              window.location.href = loginUrl;
            }, 1000);
          }
        }, 2000);
      } else {
        const errorMessage = apiError.message || 'Failed to create account. Please try again.';
        setError(errorMessage);
        toast.error(errorMessage, {
          duration: 4000,
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  if (!userInfo) {
    return (
      <div className="from-primary/20 to-secondary/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
        <Card className="w-full max-w-md border shadow-xl">
          <CardContent className="p-6">
            <p className="text-center">Loading...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="from-primary/20 to-secondary/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
      <Card className="w-full max-w-md border shadow-xl">
        <CardHeader className="space-y-1">
          <div className="text-center">
            <Image
              src="/images/logo.png"
              alt="NGnair Logo"
              width={330}
              height={81}
              priority
              quality={100}
              className="mx-auto h-16"
            />
          </div>
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Set Your Password
            </h2>
            <p className="text-sm text-gray-600">
              Welcome {userInfo.firstName}! Please create a secure password for your account.
            </p>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <LockIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="password"
                placeholder="Enter your password"
                type={showPassword ? "text" : "password"}
                className="pl-10 pr-10"
                value={form.password}
                onChange={(e) => setForm({ ...form, password: e.target.value })}
              />
              <button
                type="button"
                className="absolute right-3 top-3 h-4 w-4 text-muted-foreground hover:text-gray-700 focus:outline-none"
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>

          {form.password && (
            <div className="space-y-3">
              <PasswordRequirements password={form.password} />
              <PasswordStrengthChecker
                password={form.password}
                showScore={true}
                showFeedback={true}
                showSuggestions={true}
                className="border-t pt-3"
              />
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="confirm-password">Confirm Password</Label>
            <div className="relative">
              <LockIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="confirm-password"
                placeholder="Confirm your password"
                type={showConfirmPassword ? "text" : "password"}
                className="pl-10 pr-10"
                value={form.confirmPassword}
                onChange={(e) => setForm({ ...form, confirmPassword: e.target.value })}
              />
              <button
                type="button"
                className="absolute right-3 top-3 h-4 w-4 text-muted-foreground hover:text-gray-700 focus:outline-none"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                aria-label={showConfirmPassword ? "Hide confirm password" : "Show confirm password"}
              >
                {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>

          <Button
            className="w-full bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
            onClick={handleSubmit}
            disabled={submitting || !validatePassword(form.password) || form.password !== form.confirmPassword}
          >
            {submitting ? 'Creating Account...' : 'Create Account'}
          </Button>

          {error && (
            <p className="mx-auto -mt-2 text-center text-xs text-red-600">{error}</p>
          )}

          <div className="text-center">
            <p className="text-xs text-gray-500">
              By creating an account, you agree to our Terms of Service
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Development-only redirect tester */}
      <RedirectTester />
    </div>
  );
}
