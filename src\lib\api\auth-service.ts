// src/lib/api/auth-service.ts
import { AUTHSTORE } from '../auth-storage';
import { handleRedirect } from '../redirect-handler';

const API_BASE_URL = 'https://ng-auth-dev.dev1.ngnair.com/api/v1';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  mfa_required?: boolean;
  methods?: string[];
  message?: string;
  user_id?: string;
  mfa_token?: string;
  token?: string;
  phone_number?: string;
  // Rails backend response fields
  access_token?: string;
  device_id?: string;
  // Success response (can be boolean or string)
  success?: boolean | string;
}

export interface MfaVerifyRequest {
  user_id: string;
  otp: string;
  mfa_token: string;
}

export interface MfaVerifyResponse {
  success?: boolean;
  token?: string;
  message?: string;
  device_id?: string;  // Added to match the actual API response
}

export interface UserInfo {
  user_id: string;
  email: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
}

export interface RegisterRequest {
  email: string;
  first_name: string;
  last_name: string;
}

export interface RegisterResponse {
  message: string;
  verification_link: string;
  // We can extract user_id and token from verification_link
}

export interface VerifyEmailRequest {
  user_id: string;
  token: string;
}

export interface VerifyEmailResponse {
  message: string;
  access_token?: string;
  // On success, should provide access_token
}

export interface SendOtpRequest {
  user_id: string;
  phone: string;
}

export interface SendOtpResponse {
  message: string;
  otp: string; // For development/testing - may not be in production
}

export interface VerifyOtpRequest {
  user_id: string;
  otp: string;
}

export interface VerifyOtpResponse {
  message: string;
}

export interface SetPasswordRequest {
  user_id: string;
  password: string;
}

export interface SetPasswordResponse {
  access_token: string;
  device_id: string;
}

export interface ApiError {
  message: string;
  status: number;
  details?: any;
}

export interface JWTPayload {
  sub: string; // user_id
  iss: string; // issuer
  iat: number; // issued at
  exp: number; // expiration
  accounts?: Array<{
    id: string;
    role: string;
    scopes: string[];
  }>;
  partners?: Array<{
    id: string;
    role: string;
    scopes: string[];
  }>;
  selected_account_id?: string; // optional — active context
  selected_partner_id?: string | null; // optional — null if not in partner context
}

// Utility function to extract data from verification link
export function parseVerificationLink(verificationLink: string): { userId: string | null; token: string | null } {
  try {
    const urlParts = verificationLink.split('?');
    if (urlParts.length < 2) return { userId: null, token: null };

    const params = new URLSearchParams(urlParts[1]);
    return {
      userId: params.get('user_id'),
      token: params.get('token')
    };
  } catch {
    return { userId: null, token: null };
  }
}

class AuthService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...options.headers as Record<string, string>,
    };

    // Add device ID if available - high priority header
    if (typeof window !== 'undefined') {
      const deviceId = localStorage.getItem('device_id');
      if (deviceId) {
        defaultHeaders['X-Device-ID'] = deviceId;
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Adding device ID to request:', deviceId);
        }
      }
    }

    // Add auth token if available
    // const token = AUTHSTORE.get();
    // if (token) {
    //   defaultHeaders['Authorization'] = `Bearer ${token}`;
    // }

    try {
      const response = await fetch(url, {
        ...options,
        headers: defaultHeaders,
        credentials: 'include', // Required for Rails cookie-based auth
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        // Enhanced error logging for debugging
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.error('=== API ERROR DETAILS ===');
          console.error('URL:', url);
          console.error('Status:', response.status);
          console.error('Status Text:', response.statusText);
          console.error('Error Data:', errorData);
          console.error('Request Headers:', defaultHeaders);
          console.error('========================');
        }

        throw {
          message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status,
          details: errorData,
        } as ApiError;
      }

      return await response.json();
    } catch (error) {
      if (error && typeof error === 'object' && 'status' in error) {
        throw error;
      }
      
      throw {
        message: error instanceof Error ? error.message : 'Network error occurred',
        status: 0,
      } as ApiError;
    }
  }

  public mfaUserId: string | null = null;
  public mfaToken: string | null = null;

  async login(credentials: LoginRequest): Promise<LoginResponse> {
    // Debug logging (only in development)
    if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
      console.log('Frontend login request:');
      console.log('URL:', `${API_BASE_URL}/login`);
      console.log('Credentials:', credentials);
      
      // Log device ID if available
      if (typeof window !== 'undefined') {
        const deviceId = sessionStorage.getItem('device_id');
        console.log('Current Device ID:', deviceId || 'None');
      }
    }

    const response = await this.makeRequest<LoginResponse>('/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    // Store device ID if present in response
    if (response.device_id && typeof window !== 'undefined') {
      localStorage.setItem('device_id', response.device_id.toString());
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Device ID received in login response:', response.device_id);
      }
    }

    // Store MFA info if required
    if (response.mfa_required && response.user_id && response.mfa_token) {
      this.mfaUserId = response.user_id;
      this.mfaToken = response.mfa_token;
      
      // Store the current redirect URL for use after MFA
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        const redirectUrl = urlParams.get('redirect');
        if (redirectUrl) {
          localStorage.setItem('redirectUrl', redirectUrl);
          if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
            console.log('Stored redirect URL for after MFA:', redirectUrl);
          }
        }
      }

      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('MFA required, storing user_id and mfa_token:', response.user_id, response.mfa_token);
      }
    } else {
      this.mfaUserId = null;
      this.mfaToken = null;
    }

    return response;
  }

  async verifyMfa(otp: number | string): Promise<MfaVerifyResponse> {
    // Use stored user_id and mfa_token
    if (!this.mfaUserId || !this.mfaToken) {
      throw { message: 'Missing MFA user_id or mfa_token', status: 400 };
    }
    const mfaData: MfaVerifyRequest = {
      user_id: this.mfaUserId,
      mfa_token: this.mfaToken,
      otp: otp.toString(), // Convert to string to match backend expectations
    };

    if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
      console.log('Verifying MFA with data:', mfaData);
    }
    
    const response = await this.makeRequest<MfaVerifyResponse>('/mfa_verify', {
      method: 'POST',
      body: JSON.stringify(mfaData),
    });

    if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
      console.log('MFA verification response:', response);
    }

    // If verification is successful (we get a device_id)
    if (response.device_id && typeof window !== 'undefined') {
      // Store device_id in localStorage
      localStorage.setItem('device_id', response.device_id.toString());
      
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Device ID stored in localStorage:', response.device_id);
      }

      // Clear MFA-related data
      this.mfaUserId = null;
      this.mfaToken = null;

      // Use the centralized redirect handler
      handleRedirect();
    }

    return response;
  }

  // Optional: Add method to get user info if the API supports it
  async getUserInfo(userId: string): Promise<UserInfo> {
    return this.makeRequest<UserInfo>(`/user/${userId}`, {
      method: 'GET',
    });
  }

  // Method to validate current token using local JWT validation (no API calls)
  validateToken(): boolean {
    try {
      const token = AUTHSTORE.get();
      if (!token) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('No token found');
        }
        return false;
      }

      // Basic JWT structure validation
      const isValidJWT = this.isValidJWTStructure(token);

      if (!isValidJWT) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Token has invalid JWT structure');
        }
        return false;
      }

      // Check if token is expired
      const isExpired = this.isTokenExpired(token);

      if (isExpired) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Token is expired');
        }
        return false;
      }

      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Token validation successful (local validation only)');
      }

      return true;
    } catch (error) {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Token validation failed:', error);
      }
      return false;
    }
  }

  // Helper method to check JWT structure
  private isValidJWTStructure(token: string): boolean {
    try {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Validating JWT structure for token:', token.substring(0, 50) + '...');
        console.log('Token length:', token.length);
      }

      const parts = token.split('.');
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('JWT parts count:', parts.length);
        console.log('Part lengths:', parts.map(p => p.length));
      }

      if (parts.length !== 3) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Invalid JWT: Expected 3 parts, got', parts.length);
        }
        return false;
      }

      // Try to decode the header and payload
      let header, payload;

      try {
        header = JSON.parse(atob(parts[0]));
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('JWT header:', header);
        }
      } catch (e: unknown) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Failed to decode JWT header:', e instanceof Error ? e.message : 'Unknown error');
          console.log('Header part:', parts[0]);
        }
        return false;
      }

      try {
        payload = JSON.parse(atob(parts[1]));
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('JWT payload:', payload);
        }
      } catch (e: unknown) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Failed to decode JWT payload:', e instanceof Error ? e.message : 'Unknown error');
          console.log('Payload part:', parts[1]);
        }
        return false;
      }

      // Basic checks
      const isValid = header && payload && header.alg && payload.exp;
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('JWT validation result:', isValid);
        console.log('Header has alg:', !!header.alg);
        console.log('Payload has exp:', !!payload.exp);
      }

      return isValid;
    } catch (error) {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('JWT structure validation error:', error);
      }
      return false;
    }
  }

  // Helper method to check if token is expired
  private isTokenExpired(token: string): boolean {
    try {
      const parts = token.split('.');
      const payload = JSON.parse(atob(parts[1]));

      if (!payload.exp) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Token has no expiration claim');
        }
        return true; // No expiration claim means invalid
      }

      const currentTime = Math.floor(Date.now() / 1000);
      const isExpired = payload.exp < currentTime;

      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Token expiration check:');
        console.log('Current time:', currentTime, new Date(currentTime * 1000).toLocaleString());
        console.log('Token expires:', payload.exp, new Date(payload.exp * 1000).toLocaleString());
        console.log('Is expired:', isExpired);
      }

      return isExpired;
    } catch (error) {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Token expiration check error:', error);
      }
      return true; // If we can't decode, consider it expired
    }
  }

  // Method to get JWKS for proper JWT validation (for future implementation)
  async getJWKS(): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}/jwks`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`JWKS fetch failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('JWKS fetch failed:', error);
      }
      throw error;
    }
  }

  // Method to decode JWT payload without verification (for client-side use)
  decodeTokenPayload(): JWTPayload | null {
    try {
      const token = AUTHSTORE.get();
      if (!token) {
        return null;
      }

      const parts = token.split('.');
      if (parts.length !== 3) {
        return null;
      }

      const payload = JSON.parse(atob(parts[1]));
      return payload as JWTPayload;
    } catch (error) {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Failed to decode token payload:', error);
      }
      return null;
    }
  }

  // Method to get current user context
  getCurrentUserContext(): {
    userId: string | null;
    accounts: Array<{ id: string; role: string; scopes: string[] }>;
    partners: Array<{ id: string; role: string; scopes: string[] }>;
    selectedAccountId: string | null;
    selectedPartnerId: string | null;
  } {
    const payload = this.decodeTokenPayload();

    if (!payload) {
      return {
        userId: null,
        accounts: [],
        partners: [],
        selectedAccountId: null,
        selectedPartnerId: null,
      };
    }

    return {
      userId: payload.sub || null,
      accounts: payload.accounts || [],
      partners: payload.partners || [],
      selectedAccountId: payload.selected_account_id || null,
      selectedPartnerId: payload.selected_partner_id || null,
    };
  }

  // Method to check if user has specific scope for an account
  hasAccountScope(accountId: string, scope: string): boolean {
    const context = this.getCurrentUserContext();
    const account = context.accounts.find(acc => acc.id === accountId);
    return account ? account.scopes.includes(scope) : false;
  }

  // Method to check if user has specific scope for a partner
  hasPartnerScope(partnerId: string, scope: string): boolean {
    const context = this.getCurrentUserContext();
    const partner = context.partners.find(p => p.id === partnerId);
    return partner ? partner.scopes.includes(scope) : false;
  }

  // Registration endpoints
  async register(data: RegisterRequest): Promise<RegisterResponse> {
    return this.makeRequest<RegisterResponse>('/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async verifyEmail(data: VerifyEmailRequest): Promise<VerifyEmailResponse> {
    return this.makeRequest<VerifyEmailResponse>('/verify_email', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async sendOtp(data: SendOtpRequest): Promise<SendOtpResponse> {
    return this.makeRequest<SendOtpResponse>('/send_otp', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async verifyOtp(data: VerifyOtpRequest): Promise<VerifyOtpResponse> {
    return this.makeRequest<VerifyOtpResponse>('/verify_otp', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // This requires access token and stores the final access token
  async setPassword(data: SetPasswordRequest): Promise<SetPasswordResponse> {
    if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
      console.log('=== SET PASSWORD API CALL ===');
      console.log('Endpoint: /set_password');
      console.log('Method: POST');
      console.log('User ID:', data.user_id);
      console.log('Password length:', data.password.length);
      console.log('Full URL:', `${API_BASE_URL}/set_password`);
      console.log('============================');
    }

    try {
      const response = await this.makeRequest<any>('/set_password', {
        method: 'POST',
        body: JSON.stringify(data),
      });

      // Rails backend handles access token via httpOnly cookies
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Registration complete - Access token set via httpOnly cookie');
        console.log('Raw response received:', response);
        console.log('Response type:', typeof response);
        console.log('Response keys:', Object.keys(response || {}));
      }

      // Handle different response formats from backend
      let normalizedResponse: SetPasswordResponse;

      if (response && typeof response === 'object') {
        // Extract access_token from various possible fields
        const accessToken = response.access_token || response.token || response.jwt || '';

        // Extract device_id from various possible fields
        const deviceId = response.device_id || response.deviceId || response.device || '';

        normalizedResponse = {
          access_token: accessToken,
          device_id: deviceId
        };

        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Normalized response:', normalizedResponse);
        }
      } else {
        // Fallback for unexpected response format
        normalizedResponse = {
          access_token: '',
          device_id: ''
        };

        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.warn('Unexpected response format, using fallback');
        }
      }

      if (normalizedResponse.device_id && typeof window !== 'undefined') {
        sessionStorage.setItem('device_id', normalizedResponse.device_id);
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Device ID stored:', normalizedResponse.device_id);
        }
      }

      return normalizedResponse;
    } catch (error) {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.error('Set password error details:', error);
      }
      throw error;
    }
  }
}

export const authService = new AuthService();
