// File: src/app/layout.tsx

import "@/lib/globals.css";
import type { Metadata } from "next";
import { ClientProviders } from "./provider";

export const metadata: Metadata = {
  title: "NGnair Auth",
  description: "Authentication portal for NGnair platform users",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <ClientProviders>{children}</ClientProviders>
      </body>
    </html>
  );
}
