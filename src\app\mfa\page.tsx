// mfa/page.tsx
'use client';

import { useSearchParams } from 'next/navigation';
import { useState, Suspense } from 'react';
import { gql, useMutation } from '@apollo/client';

const VERIFY_MFA_MUTATION = gql`
  mutation VerifyMfa($code: String!) {
    verifyMfa(code: $code) {
      token
    }
  }
`;

function MfaContent() {
  const searchParams = useSearchParams();
  const redirectUri = searchParams.get('redirect_uri');
  const [code, setCode] = useState('');
  const [error, setError] = useState('');

  const [verifyMfa, { loading }] = useMutation(VERIFY_MFA_MUTATION, {
    onCompleted: (data) => {
      if (!data?.verifyMfa?.token) {
        setError('Invalid MFA code or server error.');
        return;
      }
      if (window.opener && redirectUri) {
        window.opener.postMessage({ type: 'AUTH_SUCCESS' }, redirectUri);
        window.close();
      } else {
        window.location.href = '/';
      }
    },
    onError: (err) => {
      setError(err?.message || 'MFA verification failed.');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!code.trim()) {
      setError('Please enter the MFA code.');
      return;
    }
    verifyMfa({ variables: { code } });
  };

  return (
    <main className="mfa-page">
      <h1>Two-Factor Authentication</h1>
      <form onSubmit={handleSubmit} className="form">
        <input
          type="text"
          placeholder="Enter MFA Code"
          value={code}
          onChange={(e) => setCode(e.target.value)}
          required
        />
        {error && <p className="error">{error}</p>}
        <button type="submit" disabled={loading}>
          {loading ? 'Verifying...' : 'Verify'}
        </button>
      </form>
    </main>
  );
}

export default function MfaPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <MfaContent />
    </Suspense>
  );
}
