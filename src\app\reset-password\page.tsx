'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LockIcon, Eye, EyeOff, CheckIcon } from 'lucide-react';
import PasswordStrengthChecker, { usePasswordStrength } from '@/components/PasswordStrengthChecker';
import zxcvbn from 'zxcvbn';
import Image from 'next/image';
interface PasswordRequirement {
  text: string;
  met: boolean;
}

export default function ResetPasswordPage() {
  const [token, setToken] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const passwordRequirements: PasswordRequirement[] = [
    { text: 'At least 8 characters', met: password.length >= 8 },
    { text: 'Contains uppercase letter', met: /[A-Z]/.test(password) },
    { text: 'Contains lowercase letter', met: /[a-z]/.test(password) },
    { text: 'Contains number', met: /\d/.test(password) },
    { text: 'Contains special character', met: /[!@#$%^&*(),.?":{}|<>]/.test(password) },
  ];

  usePasswordStrength(password); // For consistency with password strength validation
  const zxcvbnScore = password ? zxcvbn(password).score : 0;
  const allRequirementsMet = passwordRequirements.every(req => req.met) && zxcvbnScore >= 2;
  const passwordsMatch = password === confirmPassword && password.length > 0;

  useEffect(() => {
    // Get token from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const resetToken = urlParams.get('token');
    
    if (resetToken) {
      setToken(resetToken);
    } else {
      setError('Invalid or missing reset token. Please request a new password reset link.');
    }
  }, []);

  useEffect(() => {
    setError('');
  }, [password, confirmPassword]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!token) {
      setError('Invalid reset token. Please request a new password reset link.');
      return;
    }

    if (!allRequirementsMet) {
      setError('Please ensure your password meets all requirements and has a strength score of at least "Fair" (2/4).');
      return;
    }

    if (!passwordsMatch) {
      setError('Passwords do not match.');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      // TODO: Implement actual password reset API call
      // For now, simulate the request
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setSuccess(true);
    } catch (err) {
      setError('Failed to reset password. Please try again or request a new reset link. Error: '+err);
    } finally {
      setSubmitting(false);
    }
  };

  const redirectToLogin = () => {
    window.location.href = '/login';
  };

  if (success) {
    return (
      <div className="from-primary/20 to-secondary/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
        <Card className="w-full max-w-md border shadow-xl">
          <CardHeader className="space-y-1">
            <CardTitle className="text-center text-2xl font-bold">
              <Image
                src="/images/logo.png"
                alt="Company Logo"
                width={330}
                height={81}
                priority
                quality={100}
                className="mx-auto"
              />
            </CardTitle>
            <div className="text-left">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Password reset successful
              </h2>
              <p className="text-sm text-gray-600">
                Your password has been successfully reset. You can now sign in with your new password.
              </p>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-sm text-green-700">
                Your password has been updated successfully. Please sign in with your new credentials.
              </p>
            </div>
            
            <Button 
              onClick={redirectToLogin}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              Continue to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!token) {
    return (
      <div className="from-primary/20 to-secondary/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
        <Card className="w-full max-w-md border shadow-xl">
          <CardHeader className="space-y-1">
            <CardTitle className="text-center text-2xl font-bold">
              <Image
                src="/images/logo.png"
                alt="Company Logo"
                width={330}
                height={81}
                priority
                quality={100}
                className="mx-auto"
              />
            </CardTitle>
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Invalid Reset Link
              </h2>
              <p className="text-sm text-gray-600">
                This password reset link is invalid or has expired. Please request a new one.
              </p>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-sm text-red-700">
                The reset token is missing or invalid. Please request a new password reset link.
              </p>
            </div>
            
            <Button 
              onClick={() => window.location.href = '/forgot-password'}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
            >
              Request New Reset Link
            </Button>
            
            <Button 
              variant="outline"
              onClick={redirectToLogin}
              className="w-full cursor-pointer"
            >
              Back to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="from-primary/20 to-secondary/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
      <Card className="w-full max-w-md border shadow-xl">
        <CardHeader className="space-y-1">
          <CardTitle className="text-center text-2xl font-bold">
            <Image
              src="/images/logo.png"
              alt="Company Logo"
              width={330}
              height={81}
              priority
              quality={100}
              className="mx-auto"
            />
          </CardTitle>
          <div className="text-left">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Reset your password
            </h2>
            <p className="text-sm text-gray-600">
              Enter your new password below.
            </p>
          </div>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password">New Password</Label>
              <div className="relative">
                <LockIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  className="pl-10 pr-10"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={submitting}
                />
                <button
                  type="button"
                  className="absolute right-3 top-3 h-4 w-4 text-muted-foreground hover:text-gray-600"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm New Password</Label>
              <div className="relative">
                <LockIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  className="pl-10 pr-10"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  disabled={submitting}
                />
                <button
                  type="button"
                  className="absolute right-3 top-3 h-4 w-4 text-muted-foreground hover:text-gray-600"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            {/* Password Requirements */}
            <div className="space-y-3">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Password Requirements:</Label>
                <div className="space-y-1">
                  {passwordRequirements.map((requirement, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckIcon
                        className={`h-3 w-3 ${requirement.met ? 'text-green-600' : 'text-gray-300'}`}
                      />
                      <span className={`text-xs ${requirement.met ? 'text-green-600' : 'text-gray-500'}`}>
                        {requirement.text}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Password Strength Checker */}
              {password && (
                <PasswordStrengthChecker
                  password={password}
                  showScore={true}
                  showFeedback={true}
                  showSuggestions={true}
                  className="border-t pt-3"
                />
              )}
            </div>

            {/* Password Match Indicator */}
            {confirmPassword.length > 0 && (
              <div className="flex items-center space-x-2">
                <CheckIcon 
                  className={`h-3 w-3 ${passwordsMatch ? 'text-green-600' : 'text-red-600'}`} 
                />
                <span className={`text-xs ${passwordsMatch ? 'text-green-600' : 'text-red-600'}`}>
                  {passwordsMatch ? 'Passwords match' : 'Passwords do not match'}
                </span>
              </div>
            )}

            <Button 
              type="submit" 
              className="w-full bg-blue-600 hover:bg-blue-700 text-white" 
              disabled={submitting || !allRequirementsMet || !passwordsMatch}
            >
              {submitting ? 'Resetting Password...' : 'Reset Password'}
            </Button>
            
            {error && (
              <p className="text-center text-xs text-red-600">{error}</p>
            )}
          </CardContent>
        </form>
      </Card>
    </div>
  );
}
