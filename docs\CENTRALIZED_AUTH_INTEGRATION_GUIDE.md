# Centralized Authentication System - Complete Integration Guide

## 🎯 Overview

This guide provides comprehensive instructions for integrating your frontend application with the NGnair centralized authentication system. This system serves as the single source of truth for user authentication across all NGnair microservices.

## 🏗️ System Architecture

### Authentication Flow
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Your App      │    │   Auth Frontend  │    │   Auth Backend  │    │   Your App      │
│                 │    │                  │    │                 │    │                 │
│ 1. User needs   │───▶│ 2. Login/Register│───▶│ 3. Validate     │───▶│ 4. Redirect     │
│    auth         │    │    Process       │    │    Credentials  │    │    with Token   │
│                 │    │                  │    │                 │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Components
- **Auth Frontend**: `https://ng-auth-dev.dev1.ngnair.com` (Centralized UI)
- **Auth Backend**: `https://ng-auth-dev.dev1.ngnair.com/api/v1` (API Services)
- **Your Application**: Your microservice frontend
- **Trusted Domains**: Configured domain patterns for security

## 🚀 Quick Integration Steps

### Step 1: Redirect to Auth System

When users need authentication, redirect them to the auth frontend:

```javascript
// Basic redirect to login
const redirectToAuth = (returnUrl) => {
  const authBaseUrl = 'https://ng-auth-dev.dev1.ngnair.com';
  const encodedReturnUrl = encodeURIComponent(returnUrl || window.location.href);
  
  window.location.href = `${authBaseUrl}/login?redirect=${encodedReturnUrl}`;
};

// Redirect to registration
const redirectToRegister = (returnUrl) => {
  const authBaseUrl = 'https://ng-auth-dev.dev1.ngnair.com';
  const encodedReturnUrl = encodeURIComponent(returnUrl || window.location.href);
  
  window.location.href = `${authBaseUrl}/register?redirect=${encodedReturnUrl}`;
};

// Usage examples
redirectToAuth('https://ng-account-fe-dev.dev1.ngnair.com/dashboard');
redirectToRegister('https://ng-support-fe-dev.dev1.ngnair.com/tickets');
```

### Step 2: Handle Return from Auth System

After successful authentication, users are redirected back with authentication cookies:

```javascript
// Check authentication status on page load
useEffect(() => {
  const checkAuth = async () => {
    try {
      // Make a request to a protected endpoint to verify authentication
      const response = await fetch('/api/user/profile', {
        credentials: 'include' // Essential: includes httpOnly cookies
      });
      
      if (response.ok) {
        // User is authenticated
        setIsAuthenticated(true);
      } else if (response.status === 401) {
        // Not authenticated, redirect to auth
        redirectToAuth();
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      redirectToAuth();
    }
  };
  
  checkAuth();
}, []);
```

## 📋 URL Parameters and Configuration

### Required Parameters

#### `redirect` Parameter
- **Purpose**: Specifies where to redirect after successful authentication
- **Format**: Fully qualified URL (must be from trusted domain)
- **Encoding**: Must be URL-encoded using `encodeURIComponent()`

```javascript
// Correct usage
const returnUrl = 'https://ng-account-fe-dev.dev1.ngnair.com/dashboard?tab=settings';
const authUrl = `https://ng-auth-dev.dev1.ngnair.com/login?redirect=${encodeURIComponent(returnUrl)}`;

// With complex URLs
const complexUrl = 'https://ng-support-fe-dev.dev1.ngnair.com/tickets?status=open&priority=high';
const authUrl = `https://ng-auth-dev.dev1.ngnair.com/register?redirect=${encodeURIComponent(complexUrl)}`;
```

### Trusted Domain Patterns

Your redirect URLs must match these trusted patterns:

✅ **Allowed Domains:**
- `ng-*.dev*.ngnair.com` (e.g., `ng-account-fe-dev.dev1.ngnair.com`)
- `ng-*.ngnair.com` (e.g., `ng-support.ngnair.com`)
- `localhost:*` (development only)
- `127.0.0.1:*` (development only)

❌ **Blocked Domains:**
- External domains (e.g., `google.com`, `malicious-site.com`)
- Non-HTTPS in production
- Invalid URL formats
- JavaScript URLs (e.g., `javascript:alert()`)

### Environment-Specific URLs

```javascript
// Development
const AUTH_BASE_URL = 'https://ng-auth-dev.dev1.ngnair.com';

// Staging
const AUTH_BASE_URL = 'https://ng-auth-staging.ngnair.com';

// Production
const AUTH_BASE_URL = 'https://ng-auth.ngnair.com';
```

## 🔐 Authentication Token Handling

### Cookie-Based Authentication (Recommended)

The auth system uses secure httpOnly cookies for token storage:

```javascript
// All API requests must include credentials
const apiRequest = async (url, options = {}) => {
  return fetch(url, {
    ...options,
    credentials: 'include', // Essential: includes authentication cookies
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });
};

// Example: Fetch user data
const getUserData = async () => {
  try {
    const response = await apiRequest('/api/user/profile');
    
    if (response.status === 401) {
      // Token expired or invalid, redirect to auth
      redirectToAuth();
      return null;
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch user data:', error);
    return null;
  }
};
```

### JWT Token Validation (Advanced)

For client-side token inspection (display purposes only):

```javascript
// Decode JWT payload (for display only - never trust client-side validation)
const decodeJWTPayload = (token) => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload;
  } catch (error) {
    console.error('Invalid JWT token');
    return null;
  }
};

// Check token expiration
const isTokenExpired = (token) => {
  const payload = decodeJWTPayload(token);
  if (!payload || !payload.exp) return true;
  
  return Date.now() >= payload.exp * 1000;
};

// Extract user information from token
const getUserInfoFromToken = (token) => {
  const payload = decodeJWTPayload(token);
  if (!payload) return null;
  
  return {
    userId: payload.sub,
    email: payload.email,
    accounts: payload.accounts || [],
    partners: payload.partners || [],
    selectedAccountId: payload.selected_account_id,
    selectedPartnerId: payload.selected_partner_id,
  };
};
```

## 🔄 Complete Integration Examples

### React Integration

```jsx
import React, { useEffect, useState, createContext, useContext } from 'react';

// Auth Context
const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);

  const AUTH_BASE_URL = 'https://ng-auth-dev.dev1.ngnair.com';

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/user/profile', {
        credentials: 'include'
      });
      
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        setIsAuthenticated(true);
      } else {
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const login = (returnUrl) => {
    const redirectUrl = returnUrl || window.location.href;
    const authUrl = `${AUTH_BASE_URL}/login?redirect=${encodeURIComponent(redirectUrl)}`;
    window.location.href = authUrl;
  };

  const register = (returnUrl) => {
    const redirectUrl = returnUrl || window.location.href;
    const authUrl = `${AUTH_BASE_URL}/register?redirect=${encodeURIComponent(redirectUrl)}`;
    window.location.href = authUrl;
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsAuthenticated(false);
      setUser(null);
      login(); // Redirect to login
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return (
      <div>
        <h1>Please log in</h1>
        <button onClick={() => login()}>Login</button>
        <button onClick={() => register()}>Register</button>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={{ user, logout, login, register }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
```

### Angular Integration

```typescript
// auth.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly AUTH_BASE_URL = 'https://ng-auth-dev.dev1.ngnair.com';
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(private http: HttpClient) {
    this.checkAuthStatus();
  }

  async checkAuthStatus(): Promise<void> {
    try {
      const response = await this.http.get('/api/user/profile', {
        withCredentials: true
      }).toPromise();
      this.isAuthenticatedSubject.next(true);
    } catch {
      this.isAuthenticatedSubject.next(false);
    }
  }

  login(returnUrl?: string): void {
    const redirectUrl = returnUrl || window.location.href;
    const authUrl = `${this.AUTH_BASE_URL}/login?redirect=${encodeURIComponent(redirectUrl)}`;
    window.location.href = authUrl;
  }

  register(returnUrl?: string): void {
    const redirectUrl = returnUrl || window.location.href;
    const authUrl = `${this.AUTH_BASE_URL}/register?redirect=${encodeURIComponent(redirectUrl)}`;
    window.location.href = authUrl;
  }

  async logout(): Promise<void> {
    try {
      await this.http.post('/api/auth/logout', {}, {
        withCredentials: true
      }).toPromise();
    } catch (error) {
      console.error('Logout error:', error);
    }
    this.isAuthenticatedSubject.next(false);
    this.login();
  }
}

// auth.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(): boolean {
    const isAuthenticated = this.authService.isAuthenticatedSubject.value;
    if (!isAuthenticated) {
      this.authService.login();
      return false;
    }
    return true;
  }
}
```

## 🛡️ Security Considerations

### 1. HTTPS Enforcement
```javascript
// Always use HTTPS in production
const getAuthBaseUrl = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (isDevelopment) {
    return 'https://ng-auth-dev.dev1.ngnair.com';
  }

  // Production should always use HTTPS
  return 'https://ng-auth.ngnair.com';
};
```

### 2. Domain Validation
```javascript
// Validate redirect URLs before using them
const isValidRedirectUrl = (url) => {
  try {
    const urlObj = new URL(url);
    const domain = urlObj.hostname;

    // Check against trusted domain patterns
    const trustedPatterns = [
      /^ng-.*\.dev.*\.ngnair\.com$/,
      /^ng-.*\.ngnair\.com$/,
      /^localhost$/,
      /^127\.0\.0\.1$/
    ];

    return trustedPatterns.some(pattern => pattern.test(domain));
  } catch {
    return false;
  }
};

// Use validation before redirecting
const safeRedirectToAuth = (returnUrl) => {
  if (returnUrl && !isValidRedirectUrl(returnUrl)) {
    console.warn('Invalid redirect URL, using current page');
    returnUrl = window.location.href;
  }

  const authUrl = `${AUTH_BASE_URL}/login?redirect=${encodeURIComponent(returnUrl)}`;
  window.location.href = authUrl;
};
```

### 3. Cookie Security
```javascript
// Ensure all API requests include credentials
const secureApiRequest = async (url, options = {}) => {
  return fetch(url, {
    ...options,
    credentials: 'include', // Essential for httpOnly cookies
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest', // CSRF protection
      ...options.headers,
    },
  });
};
```

## 🧪 Testing Your Integration

### 1. Basic Authentication Flow Test
```javascript
// Test script for authentication flow
const testAuthFlow = async () => {
  console.log('🧪 Testing authentication flow...');

  // Step 1: Check initial auth status
  const initialAuth = await fetch('/api/user/profile', { credentials: 'include' });
  console.log('Initial auth status:', initialAuth.ok ? 'Authenticated' : 'Not authenticated');

  // Step 2: Test redirect URL generation
  const testRedirectUrl = 'https://ng-account-fe-dev.dev1.ngnair.com/dashboard';
  const authUrl = `https://ng-auth-dev.dev1.ngnair.com/login?redirect=${encodeURIComponent(testRedirectUrl)}`;
  console.log('Generated auth URL:', authUrl);

  // Step 3: Validate redirect URL
  const isValid = isValidRedirectUrl(testRedirectUrl);
  console.log('Redirect URL valid:', isValid);

  console.log('✅ Test completed');
};

// Run test
testAuthFlow();
```

### 2. Protected Route Test
```javascript
// Test protected endpoints
const testProtectedEndpoints = async () => {
  const endpoints = [
    '/api/user/profile',
    '/api/user/settings',
    '/api/dashboard/data'
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(endpoint, { credentials: 'include' });
      console.log(`${endpoint}: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.error(`${endpoint}: Error -`, error.message);
    }
  }
};
```

### 3. Cross-Domain Cookie Test
```javascript
// Test cross-domain cookie functionality
const testCrossDomainCookies = async () => {
  // This should work if cookies are properly configured
  const response = await fetch('https://ng-auth-dev.dev1.ngnair.com/api/v1/validate_token', {
    credentials: 'include'
  });

  console.log('Cross-domain cookie test:', response.ok ? 'PASS' : 'FAIL');

  if (!response.ok) {
    console.log('Check CORS and cookie domain configuration');
  }
};
```

## 🔧 Troubleshooting Common Issues

### Issue 1: Authentication Not Persisting
**Symptoms**: User gets redirected to login repeatedly
**Solution**:
```javascript
// Ensure credentials: 'include' is set on ALL requests
fetch('/api/protected-endpoint', {
  credentials: 'include' // This is essential!
});

// Check cookie domain configuration
// Cookies must be set for the correct domain
```

### Issue 2: Redirect Not Working
**Symptoms**: User doesn't get redirected back after login
**Solutions**:
```javascript
// 1. Check URL encoding
const correctUrl = encodeURIComponent('https://ng-account-fe-dev.dev1.ngnair.com/dashboard');

// 2. Verify domain is trusted
const isValid = isValidRedirectUrl(yourRedirectUrl);

// 3. Check for special characters in URL
const cleanUrl = yourUrl.replace(/[^\w\-._~:/?#[\]@!$&'()*+,;=]/g, '');
```

### Issue 3: CORS Errors
**Symptoms**: Network errors or CORS policy violations
**Solutions**:
```javascript
// 1. Verify the auth backend allows your domain
// 2. Check that requests include proper headers
const headers = {
  'Content-Type': 'application/json',
  'X-Requested-With': 'XMLHttpRequest'
};

// 3. Ensure credentials are included
const options = {
  credentials: 'include',
  headers
};
```

## 📞 Support and Maintenance

### Getting Help
1. **Check browser console** for detailed error messages
2. **Verify network requests** include cookies and proper headers
3. **Test redirect URLs** manually in browser
4. **Check domain configuration** against trusted patterns

### Debug Mode
Enable detailed logging in development:
```javascript
// Add to your app initialization
if (process.env.NODE_ENV === 'development') {
  window.authDebug = true;

  // Log all auth-related requests
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    if (args[0].includes('/api/')) {
      console.log('🔍 API Request:', args);
    }
    return originalFetch.apply(this, args);
  };
}
```

### Health Check Endpoint
```javascript
// Monitor auth system health
const checkAuthSystemHealth = async () => {
  try {
    const response = await fetch('https://ng-auth-dev.dev1.ngnair.com/api/v1/health');
    return response.ok;
  } catch {
    return false;
  }
};
```

## 📚 Additional Resources

- **Frontend Integration Guide**: `docs/FRONTEND_INTEGRATION_GUIDE.md`
- **HTTP 500 Debugging Guide**: `docs/HTTP_500_DEBUGGING_GUIDE.md`
- **Test Account Creation Flow**: `docs/TEST_ACCOUNT_CREATION_FLOW.md`

## 🎯 Quick Reference

### Essential URLs
- **Login**: `https://ng-auth-dev.dev1.ngnair.com/login?redirect={encoded_url}`
- **Register**: `https://ng-auth-dev.dev1.ngnair.com/register?redirect={encoded_url}`
- **API Base**: `https://ng-auth-dev.dev1.ngnair.com/api/v1`

### Essential Headers
```javascript
{
  'Content-Type': 'application/json',
  'X-Requested-With': 'XMLHttpRequest'
}
```

### Essential Options
```javascript
{
  credentials: 'include'
}
```

This guide provides everything needed to successfully integrate with the NGnair centralized authentication system. Follow the examples for your framework and refer to the troubleshooting section for common issues.

### Vue.js Integration

```javascript
// auth.js
export const authService = {
  AUTH_BASE_URL: 'https://ng-auth-dev.dev1.ngnair.com',

  async checkAuth() {
    try {
      const response = await fetch('/api/user/profile', {
        credentials: 'include'
      });
      return response.ok;
    } catch {
      return false;
    }
  },

  login(returnUrl) {
    const redirectUrl = returnUrl || window.location.href;
    const authUrl = `${this.AUTH_BASE_URL}/login?redirect=${encodeURIComponent(redirectUrl)}`;
    window.location.href = authUrl;
  },

  register(returnUrl) {
    const redirectUrl = returnUrl || window.location.href;
    const authUrl = `${this.AUTH_BASE_URL}/register?redirect=${encodeURIComponent(redirectUrl)}`;
    window.location.href = authUrl;
  },

  async logout() {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      });
    } catch (error) {
      console.error('Logout error:', error);
    }
    this.login();
  }
};

// Vue component
export default {
  data() {
    return {
      isAuthenticated: false,
      isLoading: true,
      user: null
    };
  },
  
  async mounted() {
    this.isAuthenticated = await authService.checkAuth();
    if (this.isAuthenticated) {
      await this.fetchUserData();
    }
    this.isLoading = false;
  },
  
  methods: {
    async fetchUserData() {
      try {
        const response = await fetch('/api/user/profile', {
          credentials: 'include'
        });
        if (response.ok) {
          this.user = await response.json();
        }
      } catch (error) {
        console.error('Failed to fetch user data:', error);
      }
    },
    
    login: authService.login,
    register: authService.register,
    logout: authService.logout
  }
};
```
