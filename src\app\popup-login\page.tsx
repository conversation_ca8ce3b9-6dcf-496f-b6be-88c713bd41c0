// popup-login/page.tsx
'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Image from 'next/image';

function PopupLoginContent() {
  const searchParams = useSearchParams();
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: true
  });
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  // Get URL parameters
  const redirectUri = searchParams.get('redirect_uri') || '';
  const clientId = searchParams.get('client_id') || '';
  const state = searchParams.get('state') || '';
  const scope = searchParams.get('scope') || '';
  const responseType = searchParams.get('response_type') || '';
  const initialError = searchParams.get('error') || '';

  useEffect(() => {
    if (initialError) {
      setError(initialError);
    }
  }, [initialError]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/auth/popup-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          redirect_uri: redirectUri,
          client_id: clientId,
          state,
          scope,
          response_type: responseType
        }),
      });

      const data = await response.json();

      if (data.success) {
        if (data.requiresMFA) {
          // Redirect to MFA page with popup context
          window.location.href = `/mfa?redirect_uri=${encodeURIComponent(redirectUri)}&popup=true`;
        } else if (data.authCode) {
          // Send success message to parent window
          if (window.opener && redirectUri) {
            const message = {
              type: 'AUTH_SUCCESS',
              code: data.authCode,
              state: state
            };
            window.opener.postMessage(message, redirectUri);
            window.close();
          }
        }
      } else {
        setError(data.error || 'Login failed');
      }
    } catch (err) {
      setError('An error occurred. Please try again. Error: ' + err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleCancel = () => {
    if (window.opener && redirectUri) {
      const message = {
        type: 'AUTH_CANCELLED',
        error: 'access_denied',
        error_description: 'User cancelled the authorization request',
        state: state
      };
      window.opener.postMessage(message, redirectUri);
    }
    window.close();
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white shadow-lg rounded-lg p-8">
          {/* Header */}
          <div className="text-center space-y-4 mb-6">
            <Image
              src="/images/logo.png"
              alt="NGnair Logo"
              width={330}
              height={81}
              className="mx-auto h-16 w-auto"
            />
            <h2 className="text-2xl font-bold text-gray-900">Sign in</h2>
            <p className="text-sm text-gray-600">
              Authorize access to your account
            </p>
          </div>

          {/* OAuth Info */}
          {clientId && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
              <div className="text-sm">
                <p className="text-blue-800 font-medium">Application requesting access:</p>
                <p className="text-blue-600">{clientId}</p>
                {scope && (
                  <>
                    <p className="text-blue-800 font-medium mt-2">Requested permissions:</p>
                    <p className="text-blue-600">{scope}</p>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                value={formData.email}
                onChange={handleInputChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your email"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                value={formData.password}
                onChange={handleInputChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your password"
              />
            </div>

            <div className="flex items-center">
              <input
                id="rememberMe"
                name="rememberMe"
                type="checkbox"
                checked={formData.rememberMe}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-900">
                Remember me
              </label>
            </div>

            <div className="flex space-x-4">
              <button
                type="button"
                onClick={handleCancel}
                className="flex-1 py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              
              <button
                type="submit"
                disabled={isLoading}
                className="flex-1 py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </>
                ) : (
                  'Authorize & Sign in'
                )}
              </button>
            </div>
          </form>

          {/* Footer */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              By signing in, you authorize this application to access your account.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function PopupLoginPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PopupLoginContent />
    </Suspense>
  );
}
