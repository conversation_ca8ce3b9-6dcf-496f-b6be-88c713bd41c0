/**
 * Centralized Redirect Handler
 * 
 * This module provides a centralized way to handle redirects after authentication,
 * with improved validation, error handling, and logging.
 */

import { AUTHSTORE } from './auth-storage';

// Default fallback URL from environment or hardcoded
const DEFAULT_FALLBACK_URL = process.env.NEXT_PUBLIC_FALLBACK_URL || 'https://ng-account-fe-dev.dev1.ngnair.com';

// Development mode flag
const isDevelopment = process.env.NEXT_PUBLIC_APP_ENV === 'development';

/**
 * Enhanced redirect URL validation
 */
export const validateRedirectUrl = (url: string): { isValid: boolean; reason?: string } => {
  try {
    const urlObj = new URL(url);
    
    // Check if it's a trusted origin
    if (!AUTHSTORE.isTrustedOrigin(urlObj.origin)) {
      return {
        isValid: false,
        reason: `Untrusted origin: ${urlObj.origin}. Only domains matching trusted patterns are allowed.`
      };
    }
    
    // Check protocol (HTTPS in production, allow HTTP for localhost in dev)
    if (urlObj.protocol !== 'https:' && urlObj.protocol !== 'http:') {
      return {
        isValid: false,
        reason: `Invalid protocol: ${urlObj.protocol}. Only HTTP and HTTPS are allowed.`
      };
    }
    
    // In production, enforce HTTPS except for localhost
    if (!isDevelopment && urlObj.protocol === 'http:' && !urlObj.hostname.includes('localhost') && urlObj.hostname !== '127.0.0.1') {
      return {
        isValid: false,
        reason: 'HTTP protocol not allowed in production except for localhost.'
      };
    }
    
    // Check for suspicious patterns
    const suspiciousPatterns = [
      /javascript:/i,
      /data:/i,
      /vbscript:/i,
      /file:/i,
      /ftp:/i,
    ];
    
    if (suspiciousPatterns.some(pattern => pattern.test(url))) {
      return {
        isValid: false,
        reason: 'URL contains suspicious protocol or pattern.'
      };
    }
    
    return { isValid: true };
  } catch (error) {
    return {
      isValid: false,
      reason: `Invalid URL format: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

/**
 * Extract redirect URL from various sources
 */
export const extractRedirectUrl = (): { url: string | null; source: string } => {
  if (typeof window === 'undefined') {
    return { url: null, source: 'server-side' };
  }
  
  // 1. Check URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const redirectParam = urlParams.get('redirect');
  
  if (redirectParam) {
    try {
      const decodedUrl = decodeURIComponent(redirectParam);
      return { url: decodedUrl, source: 'url-parameter' };
    } catch (error) {
      console.warn('Failed to decode redirect URL parameter:', redirectParam);
    }
  }
  
  // 2. Check localStorage (for cases where redirect was stored)
  const storedRedirect = localStorage.getItem('redirectUrl');
  if (storedRedirect) {
    localStorage.removeItem('redirectUrl'); // Clean up after use
    return { url: storedRedirect, source: 'localStorage' };
  }
  
  // 3. Check sessionStorage
  const sessionRedirect = sessionStorage.getItem('redirectUrl');
  if (sessionRedirect) {
    sessionStorage.removeItem('redirectUrl'); // Clean up after use
    return { url: sessionRedirect, source: 'sessionStorage' };
  }
  
  // 4. Check document referrer (if it's from a trusted domain)
  if (document.referrer) {
    const validation = validateRedirectUrl(document.referrer);
    if (validation.isValid) {
      return { url: document.referrer, source: 'document-referrer' };
    }
  }
  
  return { url: null, source: 'none' };
};

/**
 * Enhanced redirect handler with comprehensive logging and error handling
 */
export const handleRedirect = (customRedirectUrl?: string): void => {
  if (typeof window === 'undefined') {
    console.warn('handleRedirect called on server-side, skipping');
    return;
  }
  
  let redirectUrl: string | null = null;
  let redirectSource = 'none';
  
  // Use custom URL if provided, otherwise extract from various sources
  if (customRedirectUrl) {
    redirectUrl = customRedirectUrl;
    redirectSource = 'custom-parameter';
  } else {
    const extracted = extractRedirectUrl();
    redirectUrl = extracted.url;
    redirectSource = extracted.source;
  }
  
  // Log the redirect attempt
  if (isDevelopment) {
    console.log('🔄 Redirect Handler:', {
      redirectUrl,
      redirectSource,
      currentUrl: window.location.href,
      fallbackUrl: DEFAULT_FALLBACK_URL,
    });
  }
  
  // Validate and redirect
  if (redirectUrl) {
    const validation = validateRedirectUrl(redirectUrl);
    
    if (validation.isValid) {
      if (isDevelopment) {
        console.log('✅ Redirecting to validated URL:', redirectUrl);
      }
      
      // Add a small delay to ensure any pending operations complete
      setTimeout(() => {
        window.location.href = redirectUrl;
      }, 100);
      
      return;
    } else {
      console.warn('❌ Invalid redirect URL:', {
        url: redirectUrl,
        reason: validation.reason,
        source: redirectSource,
      });
      
      // Store the invalid attempt for debugging
      if (isDevelopment) {
        sessionStorage.setItem('lastInvalidRedirect', JSON.stringify({
          url: redirectUrl,
          reason: validation.reason,
          timestamp: new Date().toISOString(),
        }));
      }
    }
  }
  
  // Fallback to default URL
  if (isDevelopment) {
    console.log('🔄 Using fallback URL:', DEFAULT_FALLBACK_URL);
  }
  
  setTimeout(() => {
    window.location.href = DEFAULT_FALLBACK_URL;
  }, 100);
};

/**
 * Store redirect URL for later use (useful for MFA flows)
 */
export const storeRedirectUrl = (url: string, storage: 'localStorage' | 'sessionStorage' = 'localStorage'): boolean => {
  if (typeof window === 'undefined') return false;
  
  const validation = validateRedirectUrl(url);
  if (!validation.isValid) {
    console.warn('Cannot store invalid redirect URL:', validation.reason);
    return false;
  }
  
  try {
    if (storage === 'localStorage') {
      localStorage.setItem('redirectUrl', url);
    } else {
      sessionStorage.setItem('redirectUrl', url);
    }
    
    if (isDevelopment) {
      console.log(`📦 Stored redirect URL in ${storage}:`, url);
    }
    
    return true;
  } catch (error) {
    console.error('Failed to store redirect URL:', error);
    return false;
  }
};

/**
 * Clear stored redirect URLs
 */
export const clearStoredRedirectUrls = (): void => {
  if (typeof window === 'undefined') return;
  
  localStorage.removeItem('redirectUrl');
  sessionStorage.removeItem('redirectUrl');
  
  if (isDevelopment) {
    console.log('🧹 Cleared stored redirect URLs');
  }
};

/**
 * Get redirect debug information
 */
export const getRedirectDebugInfo = () => {
  if (typeof window === 'undefined') {
    return { error: 'Server-side execution' };
  }
  
  const { url, source } = extractRedirectUrl();
  const validation = url ? validateRedirectUrl(url) : { isValid: false, reason: 'No URL found' };
  
  return {
    currentUrl: window.location.href,
    extractedUrl: url,
    extractedSource: source,
    validation,
    fallbackUrl: DEFAULT_FALLBACK_URL,
    trustedPatterns: AUTHSTORE.getTrustedPatterns(),
    storedUrls: {
      localStorage: localStorage.getItem('redirectUrl'),
      sessionStorage: sessionStorage.getItem('redirectUrl'),
    },
    lastInvalidRedirect: sessionStorage.getItem('lastInvalidRedirect'),
  };
};

/**
 * React hook for redirect handling
 */
export const useRedirectHandler = () => {
  return {
    handleRedirect,
    validateRedirectUrl,
    extractRedirectUrl,
    storeRedirectUrl,
    clearStoredRedirectUrls,
    getDebugInfo: getRedirectDebugInfo,
  };
};

export default {
  handleRedirect,
  validateRedirectUrl,
  extractRedirectUrl,
  storeRedirectUrl,
  clearStoredRedirectUrls,
  getRedirectDebugInfo,
};
