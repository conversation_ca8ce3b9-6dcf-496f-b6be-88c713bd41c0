'use client';

import { useMemo } from 'react';
import zxcvbn from 'zxcvbn';
import { CheckIcon, AlertTriangleIcon, XIcon } from 'lucide-react';

interface PasswordStrengthCheckerProps {
  password: string;
  className?: string;
  showScore?: boolean;
  showFeedback?: boolean;
  showSuggestions?: boolean;
}

const getScoreColor = (score: number): string => {
  switch (score) {
    case 0:
    case 1:
      return 'text-red-600';
    case 2:
      return 'text-orange-500';
    case 3:
      return 'text-yellow-500';
    case 4:
      return 'text-green-600';
    default:
      return 'text-gray-500';
  }
};

const getScoreText = (score: number): string => {
  switch (score) {
    case 0:
      return 'Very Weak';
    case 1:
      return 'Weak';
    case 2:
      return 'Fair';
    case 3:
      return 'Good';
    case 4:
      return 'Strong';
    default:
      return 'Unknown';
  }
};

const getScoreIcon = (score: number) => {
  switch (score) {
    case 0:
    case 1:
      return <XIcon className="h-3 w-3" />;
    case 2:
      return <AlertTriangleIcon className="h-3 w-3" />;
    case 3:
    case 4:
      return <CheckIcon className="h-3 w-3" />;
    default:
      return null;
  }
};

export default function PasswordStrengthChecker({
  password,
  className = '',
  showScore = true,
  showFeedback = true,
  showSuggestions = false,
}: PasswordStrengthCheckerProps) {
  const result = useMemo(() => {
    if (!password) return null;
    return zxcvbn(password);
  }, [password]);

  if (!result || !password) {
    return null;
  }

  const scoreColor = getScoreColor(result.score);
  const scoreText = getScoreText(result.score);
  const scoreIcon = getScoreIcon(result.score);

  return (
    <div className={`space-y-2 ${className}`}>
      {showScore && (
        <div className="flex items-center space-x-2">
          <div className={`flex items-center space-x-1 ${scoreColor}`}>
            {scoreIcon}
            <span className="text-sm font-medium">
              Password Strength: {scoreText}
            </span>
          </div>
          <div className="flex-1">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  result.score === 0 || result.score === 1
                    ? 'bg-red-500'
                    : result.score === 2
                    ? 'bg-orange-500'
                    : result.score === 3
                    ? 'bg-yellow-500'
                    : 'bg-green-500'
                }`}
                style={{ width: `${(result.score + 1) * 20}%` }}
              />
            </div>
          </div>
        </div>
      )}

      {showFeedback && (result.feedback.warning || result.feedback.suggestions.length > 0) && (
        <div className="space-y-1">
          {result.feedback.warning && (
            <div className="flex items-start space-x-2">
              <AlertTriangleIcon className="h-3 w-3 text-orange-500 mt-0.5 flex-shrink-0" />
              <p className="text-xs text-orange-600">
                {result.feedback.warning}
              </p>
            </div>
          )}
          
          {showSuggestions && result.feedback.suggestions.length > 0 && (
            <div className="space-y-1">
              {result.feedback.suggestions.map((suggestion, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <CheckIcon className="h-3 w-3 text-blue-500 mt-0.5 flex-shrink-0" />
                  <p className="text-xs text-blue-600">
                    {suggestion}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Crack time estimate */}
      {result.crack_times_display.offline_slow_hashing_1e4_per_second && (
        <div className="text-xs text-gray-500">
          Time to crack: {result.crack_times_display.offline_slow_hashing_1e4_per_second}
        </div>
      )}
    </div>
  );
}

/**
 * Hook to get password strength validation
 */
export function usePasswordStrength(password: string) {
  const result = useMemo(() => {
    if (!password) return null;
    return zxcvbn(password);
  }, [password]);

  return {
    result,
    score: result?.score ?? 0,
    isWeak: result ? result.score < 2 : true,
    isStrong: result ? result.score >= 3 : false,
    feedback: result?.feedback,
    crackTime: result?.crack_times_display,
  };
}
