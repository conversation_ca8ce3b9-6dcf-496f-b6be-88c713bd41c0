/**
 * Authentication Redirect Utilities
 * 
 * This module provides utilities for microservices to integrate with the NGnair auth service.
 * It handles proper URL generation, validation, and redirect flows.
 */

// Default auth service URL - can be overridden via environment variables
const DEFAULT_AUTH_SERVICE_URL = 'https://ng-auth-dev.dev1.ngnair.com';

/**
 * Get the auth service URL from environment or use default
 */
export const getAuthServiceUrl = (): string => {
  if (typeof window !== 'undefined') {
    // Client-side environment variables
    return (
      process.env.NEXT_PUBLIC_AUTH_SERVICE_URL ||
      process.env.REACT_APP_AUTH_SERVICE_URL ||
      process.env.VUE_APP_AUTH_SERVICE_URL ||
      DEFAULT_AUTH_SERVICE_URL
    );
  } else {
    // Server-side environment variables
    return process.env.AUTH_SERVICE_URL || DEFAULT_AUTH_SERVICE_URL;
  }
};

/**
 * Generate authentication redirect URL
 * 
 * @param returnUrl - URL to redirect back to after authentication (defaults to current URL)
 * @param authPage - Auth page to redirect to ('login' | 'register' | 'forgot-password')
 * @returns Complete auth service URL with redirect parameter
 */
export const generateAuthRedirectUrl = (
  returnUrl?: string,
  authPage: 'login' | 'register' | 'forgot-password' = 'login'
): string => {
  const authServiceUrl = getAuthServiceUrl();
  const redirectUrl = returnUrl || (typeof window !== 'undefined' ? window.location.href : '');
  const encodedRedirect = encodeURIComponent(redirectUrl);
  
  return `${authServiceUrl}/${authPage}?redirect=${encodedRedirect}`;
};

/**
 * Redirect to auth service for authentication
 * 
 * @param returnUrl - URL to redirect back to after authentication
 * @param authPage - Auth page to redirect to
 */
export const redirectToAuth = (
  returnUrl?: string,
  authPage: 'login' | 'register' | 'forgot-password' = 'login'
): void => {
  if (typeof window !== 'undefined') {
    const authUrl = generateAuthRedirectUrl(returnUrl, authPage);
    window.location.href = authUrl;
  }
};

/**
 * Check if a URL is from a trusted domain
 * This uses the same validation logic as the auth service
 * 
 * @param url - URL to validate
 * @returns true if the URL is from a trusted domain
 */
export const isTrustedDomain = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const domain = urlObj.hostname;
    
    // Default trusted patterns (should match auth service configuration)
    const trustedPatterns = [
      /^ng-.*-fe-.*\.dev.*\.ngnair\.com$/i,     // ng-support-fe-dev.dev1.ngnair.com
      /^ng-.*\.ng-.*\.ngnair\.com$/i,           // ng-ob.ng-dev.ngnair.com
      /^ng-.*\.dev.*\.ngnair\.com$/i,           // ng-[service].dev[env].ngnair.com
      /^ng-.*-fe-.*\.ngnair\.com$/i,            // ng-[service]-fe-[env].ngnair.com
      /^.*\.ngnair\.com$/i,                     // All subdomains of ngnair.com
      /^ngnair\.com$/i,                         // Exact match
      /^localhost$/i,                           // Development
      /^127\.0\.0\.1$/i,                        // Local development
    ];
    
    return trustedPatterns.some(pattern => pattern.test(domain));
  } catch (e) {
    console.warn('Invalid URL for domain validation:', url);
    return false;
  }
};

/**
 * Extract and validate redirect URL from current page
 * 
 * @returns Validated redirect URL or null if invalid/missing
 */
export const getValidatedRedirectUrl = (): string | null => {
  if (typeof window === 'undefined') return null;
  
  const urlParams = new URLSearchParams(window.location.search);
  const redirectUrl = urlParams.get('redirect');
  
  if (!redirectUrl) return null;
  
  try {
    const decodedUrl = decodeURIComponent(redirectUrl);
    return isTrustedDomain(decodedUrl) ? decodedUrl : null;
  } catch (e) {
    console.warn('Invalid redirect URL:', redirectUrl);
    return null;
  }
};

/**
 * Authentication state checker
 * 
 * @returns true if user appears to be authenticated
 */
export const isAuthenticated = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  // Check for auth token in various storage locations
  const cookieToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('health-token='))
    ?.split('=')[1];
    
  const localStorageToken = localStorage.getItem('health-token');
  
  return !!(cookieToken || localStorageToken);
};

/**
 * Auth guard hook for React components
 * Automatically redirects to auth service if not authenticated
 * 
 * @param returnUrl - Custom return URL (defaults to current URL)
 * @param authPage - Auth page to redirect to
 * @returns Authentication status
 */
export const useAuthGuard = (
  returnUrl?: string,
  authPage: 'login' | 'register' | 'forgot-password' = 'login'
): boolean => {
  const authenticated = isAuthenticated();
  
  if (typeof window !== 'undefined' && !authenticated) {
    // Small delay to prevent redirect loops
    setTimeout(() => {
      redirectToAuth(returnUrl, authPage);
    }, 100);
  }
  
  return authenticated;
};

/**
 * Logout utility
 * Clears authentication tokens and optionally redirects to auth service
 * 
 * @param redirectToLogin - Whether to redirect to login page after logout
 * @param returnUrl - URL to return to after re-authentication
 */
export const logout = (redirectToLogin: boolean = true, returnUrl?: string): void => {
  if (typeof window === 'undefined') return;
  
  // Clear authentication tokens
  document.cookie = 'health-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.ngnair.com;';
  localStorage.removeItem('health-token');
  sessionStorage.clear();
  
  if (redirectToLogin) {
    redirectToAuth(returnUrl);
  }
};

/**
 * Configuration object for easy integration
 */
export const AuthConfig = {
  serviceUrl: getAuthServiceUrl(),
  generateUrl: generateAuthRedirectUrl,
  redirect: redirectToAuth,
  isAuthenticated,
  logout,
  isTrustedDomain,
  getValidatedRedirectUrl,
} as const;

/**
 * Express.js middleware helper
 * For Node.js/Express applications
 */
export const createAuthMiddleware = (options: {
  fallbackUrl?: string;
  authPage?: 'login' | 'register' | 'forgot-password';
} = {}) => {
  return (req: any, res: any, next: any) => {
    const token = req.cookies?.['health-token'] || req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      const returnUrl = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
      const authUrl = generateAuthRedirectUrl(returnUrl, options.authPage);
      return res.redirect(authUrl);
    }
    
    // TODO: Add token validation logic here
    // For now, assume token presence means authenticated
    next();
  };
};

export default AuthConfig;
