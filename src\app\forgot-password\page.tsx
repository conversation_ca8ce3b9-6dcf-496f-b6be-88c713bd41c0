'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { MailIcon } from 'lucide-react';
import Image from 'next/image';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      setError('Please enter your email address');
      return;
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      // TODO: Implement actual password reset request API call
      // For now, simulate the request
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setSuccess(true);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      setError('Failed to send reset email. Error code: ' + errorMsg);
    } finally {
      setSubmitting(false);
    }
  };

  const redirectToLogin = () => {
    router.push('/login');
  };

  if (success) {
    return (
      <div className="from-primary/20 to-secondary/20 dark:from-primary/10 dark:to-secondary/10 flex min-h-screen items-center justify-center bg-gradient-to-br dark:bg-gray-900 p-4">
        <Card className="w-full max-w-md border shadow-xl">
          <CardHeader className="space-y-1">
            <CardTitle className="text-center text-2xl font-bold">
              <Image
                src="/images/logo.png"
                alt="Company Logo"
                width={330}
                height={81}
                priority
                quality={100}
                className="mx-auto"
              />
            </CardTitle>
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Check your email
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                We&apos;ve sent a password reset link to <strong>{email}</strong>
              </p>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <p className="text-sm text-green-700 dark:text-green-400" aria-live="polite">
                If you don&apos;t see the email in your inbox, please check your spam folder.
              </p>
            </div>
          </CardContent>
          <CardFooter>
            <div className="w-full text-center text-sm text-gray-600 dark:text-gray-400">
              Remember your password?{' '}
              <button
                onClick={redirectToLogin}
                className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 h-auto p-0 font-normal cursor-pointer"
              >
                Sign in
              </button>
            </div>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="from-primary/20 to-secondary/20 dark:from-primary/10 dark:to-secondary/10 flex min-h-screen items-center justify-center bg-gradient-to-br dark:bg-gray-900 p-4">
      <Card className="w-full max-w-md border shadow-xl">
        <CardHeader className="space-y-1">
          <CardTitle className="text-center text-2xl font-bold">
            <Image
              src="/images/logo.png"
              alt="Company Logo"
              width={330}
              height={81}
              priority
              quality={100}
              className="mx-auto"
            />
          </CardTitle>
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Forgot your password?
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Enter your email address and we&apos;ll send you a link to reset your password.
            </p>
          </div>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <div className="relative">
                <MailIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="email"
                  placeholder="<EMAIL>"
                  type="email"
                  className="pl-10"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={submitting}
                />
              </div>
            </div>

            <Button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white cursor-pointer"
              disabled={submitting}
            >
              {submitting ? 'Sending...' : 'Send reset link'}
            </Button>

            {error && (
              <p className="text-center text-xs text-red-600 dark:text-red-400" aria-live="polite">{error}</p>
            )}
          </CardContent>
        </form>
        <CardFooter>
          <div className="w-full text-center text-sm text-gray-600 dark:text-gray-400">
            Remember your password?{' '}
            <button
              onClick={redirectToLogin}
              className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 h-auto p-0 font-normal cursor-pointer"
            >
              Sign in
            </button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
// ...existing code...
