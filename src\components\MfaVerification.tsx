// src/components/MfaVerification.tsx
'use client';

import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from './ui/card';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { ShieldIcon, ArrowLeftIcon, ClockIcon } from 'lucide-react';
import { authService, ApiError } from '../lib/api/auth-service';
import { AUTHSTORE } from '../lib/auth-storage';

interface MfaVerificationProps {
  userId: string;
  mfaToken: string;
  phoneNumber?: string;
  onSuccess?: () => void;
  onBack?: () => void;
  onTimeout?: () => void;
  className?: string;
}

export default function MfaVerification({
  userId,
  mfaToken,
  phoneNumber,
  onSuccess,
  onBack,
  onTimeout,
  className = ""
}: MfaVerificationProps) {
  const [otpCode, setOtpCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(15 * 60); // 15 minutes in seconds

  // Phone number masking function
  const maskPhoneNumber = (phone: string): string => {
    if (!phone) return '';

    // In development/staging, show full number
    if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
      return phone;
    }

    // In production, mask all but last 4 digits
    if (phone.length <= 4) {
      return phone; // If phone is 4 digits or less, show as is
    }

    const lastFour = phone.slice(-4);
    const maskedPart = '*'.repeat(phone.length - 4);
    return maskedPart + lastFour;
  };

  // Timer effect
  useEffect(() => {
    if (timeLeft <= 0) {
      if (onTimeout) {
        onTimeout();
      }
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft, onTimeout]);

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!otpCode || otpCode.length !== 6) {
      setError('Please enter a valid 6-digit OTP code.');
      return;
    }

    setLoading(true);

    try {
      // Ensure service has correct MFA context
      authService.mfaUserId = userId;
      authService.mfaToken = mfaToken;
      const response = await authService.verifyMfa(parseInt(otpCode, 10));

      if (response.success && response.token) {
        // Store the authentication token
        AUTHSTORE.set(response.token);
        
        if (onSuccess) {
          onSuccess();
        } else {
          // Default behavior: redirect to original microservice or fallback
          const searchParams = new URLSearchParams(window.location.search);
          const redirectTo = searchParams.get('redirect');

          if (redirectTo) {
            // Redirect back to the original microservice
            if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
              console.log('MFA complete - Redirecting back to microservice:', redirectTo);
            }
            window.location.href = redirectTo;
          } else {
            // Fallback to accounts frontend using environment variable
            const fallbackUrl = process.env.NEXT_PUBLIC_FALLBACK_URL || 'https://ng-account-fe-dev.dev1.ngnair.com';
            if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
              console.log('MFA complete - No redirect specified, using fallback:', fallbackUrl);
            }
            window.location.href = fallbackUrl;
          }
        }
      } else {
        setError(response.message || 'Invalid OTP code. Please try again.');
      }
    } catch (err) {
      console.error('MFA verification error:', err);
      const apiError = err as ApiError;
      setError(apiError.message || 'An error occurred during verification.');
    }
  };

  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ''); // Only allow digits
    if (value.length <= 6) {
      setOtpCode(value);
    }
  };

  return (
    <div className={`min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8 ${className}`}>
      <Card className="w-full max-w-md space-y-8">
        <CardHeader className="text-center">
          <div className="mx-auto h-16 w-auto">
            <img
              className="mx-auto h-16 w-auto"
              src="/images/logo.png"
              alt="Company Logo"
            />
          </div>
          <CardTitle className="mt-6 text-left text-3xl font-extrabold text-gray-900 dark:text-gray-100">
            Two-Factor Authentication
          </CardTitle>
          <p className="text-left text-sm text-gray-600 dark:text-gray-400 mt-2">
            Enter the 6-digit verification code sent to your registered phone number
            {phoneNumber && (
              <span className="block mt-1 font-medium text-gray-800 dark:text-gray-200">
                {maskPhoneNumber(phoneNumber)}
              </span>
            )}
          </p>
          <div className="mt-4 flex items-center justify-center bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
            <ClockIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
            <span className="text-sm font-medium text-blue-800 dark:text-blue-300">
              Time remaining: <span className="font-mono text-lg">{formatTime(timeLeft)}</span>
            </span>
          </div>
          {timeLeft <= 60 && (
            <p className="text-center text-sm text-red-600 dark:text-red-400 mt-2 font-medium">
              ⚠️ Less than 1 minute remaining!
            </p>
          )}
        </CardHeader>
        {loading ? (
          <div className="flex flex-col items-center justify-center mb-8">
            <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-4"></div>
            <span className="text-lg font-semibold text-blue-700 dark:text-blue-300">Verifying...</span>
          </div>
        ) : (
          <CardContent>
            <form className="space-y-6" onSubmit={handleSubmit}>
              {error && (
                <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4 border border-red-200 dark:border-red-800">
                  <div className="text-sm text-red-700 dark:text-red-400">{error}</div>
                </div>
              )}

              <div>
                <Label htmlFor="otp" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Verification Code
                </Label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <ShieldIcon className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  </div>
                  <Input
                    id="otp"
                    name="otp"
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    autoComplete="one-time-code"
                    required
                    className="pl-10 text-center text-lg tracking-widest"
                    placeholder="000000"
                    value={otpCode}
                    onChange={handleOtpChange}
                    maxLength={6}
                  />
                </div>
              </div>

              <div className="flex space-x-4">
                {onBack && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onBack}
                    className="flex-1 flex items-center justify-center"
                  >
                    <ArrowLeftIcon className="h-4 w-4 mr-2" />
                    Back
                  </Button>
                )}
                <Button
                  type="submit"
                  disabled={loading || otpCode.length !== 6}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 disabled:opacity-50 dark:bg-blue-700 dark:hover:bg-blue-800 dark:focus:ring-blue-600"
                >
                  {loading ? 'Verifying...' : 'Verify'}
                </Button>
              </div>
            </form>
          </CardContent>
        )}
      </Card>
    </div>
  );
}
